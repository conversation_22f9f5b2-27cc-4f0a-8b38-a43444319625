#!/usr/bin/env python3
"""
Options Data Ingestion Agent - Enhanced for Training Pipeline

Features:
📊 1. Historical Data Download with Date Range
- Download historical data for specified date ranges
- Index data download first to calculate strike ranges
- Smart strike range calculation (±1000 NIFTY, ±2000 BANKNIFTY)
- Multi-timeframe generation (1min → 3min, 5min, 15min)

⚡ 2. High-Performance Processing
- Polars + PyArrow for fast data processing
- Brotli compression for optimal storage
- Vectorized operations for efficiency
- Memory-optimized batch processing

🎯 3. Training Pipeline Integration
- Mode detection (training vs live)
- Minimal initialization for training mode
- No websocket subscription in training mode
- Comprehensive historical data coverage
"""

import asyncio
import logging
import polars as pl
import pyarrow as pa
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import aiohttp
import aiofiles
from dataclasses import dataclass
import json
import os
import requests
from dotenv import load_dotenv
import time

# SmartAPI imports
from SmartApi import SmartConnect
import pyotp

# Technical indicators (Polars-based)
from agents.polars_technical_indicators_manager import PolarsTechnicalIndicatorsManager

# Enhanced download manager
from agents.enhanced_download_manager import EnhancedDownloadManager, DownloadTask, DownloadResult
# API rate limiting
from utils.api_rate_limiter import api_rate_limiter
# Heartbeat utility
from utils.heartbeat import create_heartbeat

logger = logging.getLogger(__name__)

@dataclass
class OptionsContract:
    """Options contract specification"""
    symbol: str
    underlying: str  # NIFTY or BANKNIFTY
    strike_price: float
    expiry_date: str
    option_type: str  # CE or PE
    lot_size: int
    token: str
    exchange: str = "NFO"

@dataclass
class OptionsDataConfig:
    """Configuration for options data ingestion"""
    # SmartAPI credentials
    api_key: str
    client_id: str
    password: str
    totp_secret: str
    
    # Data settings
    underlying_symbols: List[str] = None
    data_path: str = "data"
    historical_days: int = 365
    
    # Processing settings
    chunk_size: int = 10000
    max_workers: int = 4
    
    def __post_init__(self):
        if self.underlying_symbols is None:
            self.underlying_symbols = ["NIFTY", "BANKNIFTY"]

class OptionsDataIngestionAgent:
    """
    Enhanced Options Data Ingestion Agent for Training Pipeline
    
    Handles:
    - Historical options data download with date ranges
    - Smart strike range calculation
    - Multi-timeframe data generation
    - Training pipeline integration
    - High-performance data processing
    """
    
    def __init__(self, config_path: str = "config/options_data_ingestion_config.yaml"):
        """Initialize Options Data Ingestion Agent"""
        self.config_path = config_path
        self.config = None
        self.smart_api = None
        self.is_running = False
        
        # Data storage paths
        self.data_path = Path("data")
        self.historical_path = self.data_path / "historical"
        self.live_path = self.data_path / "live"
        self.option_chains_path = self.data_path / "option_chains"

        # Multi-timeframe directories
        self.timeframes = ["1min", "3min", "5min", "15min"]

        # Create directories for each timeframe
        for timeframe in self.timeframes:
            (self.historical_path / timeframe).mkdir(parents=True, exist_ok=True)
            (self.live_path / timeframe).mkdir(parents=True, exist_ok=True)

        self.option_chains_path.mkdir(parents=True, exist_ok=True)
        
        # Options contracts cache
        self.options_contracts = {}
        self.contract_tokens = {}

        # Technical indicators manager (Polars-based)
        self.indicators_manager = PolarsTechnicalIndicatorsManager()

        # Enhanced download manager (will be initialized after SmartAPI connection)
        self.download_manager = None
        
        # Heartbeat mechanism
        self.heartbeat = create_heartbeat('data_ingestion')

        # Download configuration with proper rate limiting
        self.download_config = {
            'max_workers': 6,  # Reduced workers to prevent API limits
            'max_calls_per_minute': 80,  # Conservative API rate limit
            'min_sleep_time': 0.4,  # 0.4s sleep time as requested
            'max_retries': 3,
            'retry_delay': 1.0,
            'exponential_backoff': True
        }

        logger.info("[INIT] Options Data Ingestion Agent initialized")
    
    async def initialize(self, **kwargs):
        """Initialize the agent with mode-specific initialization"""
        try:
            # Load configuration
            await self._load_config()
            
            # Check if this is training pipeline mode
            from_date = kwargs.get('from_date')
            to_date = kwargs.get('to_date')
            is_training_mode = bool(from_date and to_date)
            
            if is_training_mode:
                logger.info("[INIT] Training pipeline mode - minimal initialization")
                # Only initialize SmartAPI for historical data download
                await self._initialize_smartapi()
                # Don't load options contracts or initialize websockets in training mode
            else:
                logger.info("[INIT] Live trading mode - full initialization")
                # Initialize SmartAPI connection
                await self._initialize_smartapi()

                # Load options contracts for live trading
                await self._load_options_contracts()

            # Clean up old JSON files to prevent flooding (both modes)
            await self._cleanup_old_json_files()

            # Start heartbeat
            self.heartbeat.start_heartbeat()
            
            logger.info("[SUCCESS] Options Data Ingestion Agent initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize agent: {e}")
            return False

    async def _cleanup_old_json_files(self):
        """Clean up old JSON files to prevent directory flooding"""
        try:
            from datetime import datetime, timedelta
            import os

            # Define cutoff time (files older than 2 hours will be removed)
            cutoff_time = datetime.now() - timedelta(hours=2)
            cutoff_timestamp = cutoff_time.timestamp()

            cleaned_count = 0
            data_path = Path("data")

            # Clean up live index JSON files
            live_index_path = data_path / "live" / "index"
            if live_index_path.exists():
                for json_file in live_index_path.glob("*.json"):
                    try:
                        # Check file modification time
                        file_mtime = json_file.stat().st_mtime
                        if file_mtime < cutoff_timestamp:
                            json_file.unlink()
                            cleaned_count += 1
                            logger.debug(f"[CLEANUP] Removed old JSON file: {json_file.name}")
                    except Exception as e:
                        logger.warning(f"[CLEANUP] Failed to remove {json_file}: {e}")

            # Clean up status reports (keep only last 50)
            status_reports_path = data_path / "status_reports"
            if status_reports_path.exists():
                status_files = list(status_reports_path.glob("system_status_*.json"))
                if len(status_files) > 50:
                    # Sort by modification time and keep only the latest 50
                    status_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
                    for old_file in status_files[50:]:
                        try:
                            old_file.unlink()
                            cleaned_count += 1
                            logger.debug(f"[CLEANUP] Removed old status report: {old_file.name}")
                        except Exception as e:
                            logger.warning(f"[CLEANUP] Failed to remove {old_file}: {e}")

            if cleaned_count > 0:
                logger.info(f"[CLEANUP] Removed {cleaned_count} old files to prevent directory flooding")
            else:
                logger.debug("[CLEANUP] No old files to clean up")

        except Exception as e:
            logger.error(f"[ERROR] Failed to cleanup old files: {e}")

    async def _load_config(self):
        """Load configuration from .env file"""
        try:
            # Load environment variables from .env file
            load_dotenv()

            # Load SmartAPI credentials from environment
            api_key = os.getenv("SMARTAPI_API_KEY")
            username = os.getenv("SMARTAPI_USERNAME")
            password = os.getenv("SMARTAPI_PASSWORD")
            totp_secret = os.getenv("SMARTAPI_TOTP_TOKEN")

            if not all([api_key, username, password, totp_secret]):
                raise ValueError("Missing SmartAPI credentials in .env file")

            # Load expiry dates from environment
            nifty_expiry_raw = os.getenv("NIFTY_EXPIRY", "14AUG2025")
            banknifty_expiry_raw = os.getenv("BANKNIFTY_EXPIRY", "28AUG2025")
            
            # Convert expiry format for SmartAPI
            nifty_expiry = self._convert_expiry_format(nifty_expiry_raw)
            banknifty_expiry = self._convert_expiry_format(banknifty_expiry_raw)

            self.config = OptionsDataConfig(
                api_key=api_key,
                client_id=username,
                password=password,
                totp_secret=totp_secret
            )

            # Store expiry dates for easy access
            self.expiry_dates = {
                'NIFTY': nifty_expiry,
                'BANKNIFTY': banknifty_expiry
            }
            
            # Store raw expiry dates for file naming
            self.expiry_dates_raw = {
                'NIFTY': nifty_expiry_raw,
                'BANKNIFTY': banknifty_expiry_raw
            }

            logger.info(f"[CONFIG] Configuration loaded successfully from .env file")
            logger.info(f"[CONFIG] Expiry dates - NIFTY: {nifty_expiry} ({nifty_expiry_raw}), BANKNIFTY: {banknifty_expiry} ({banknifty_expiry_raw})")
            logger.info(f"[CONFIG] Only instruments with these exact expiry dates will be downloaded")

        except Exception as e:
            logger.error(f"[ERROR] Failed to load configuration: {e}")
            raise
    
    def _convert_expiry_format(self, expiry_date: str) -> str:
        """Convert expiry date from DDMMMYYYY to DD-MMM-YYYY format for SmartAPI"""
        try:
            # Parse the date in DDMMMYYYY format
            date_obj = datetime.strptime(expiry_date, "%d%b%Y")
            # Convert to DD-MMM-YYYY format
            return date_obj.strftime("%d-%b-%Y").upper()
        except ValueError:
            logger.warning(f"[WARNING] Invalid expiry date format: {expiry_date}, using as-is")
            return expiry_date
    
    async def _initialize_smartapi(self):
        """Initialize SmartAPI connection"""
        try:
            # Initialize SmartConnect
            self.smart_api = SmartConnect(api_key=self.config.api_key)

            # Generate TOTP
            totp = pyotp.TOTP(self.config.totp_secret).now()

            # Login
            data = self.smart_api.generateSession(
                self.config.client_id,
                self.config.password,
                totp
            )

            if data['status']:
                logger.info("[SUCCESS] SmartAPI connection established")

                # Initialize enhanced download manager
                self.download_manager = EnhancedDownloadManager(
                    smart_api=self.smart_api,
                    **self.download_config
                )
                logger.info("[SUCCESS] Enhanced download manager initialized")

                return True
            else:
                logger.error(f"[ERROR] SmartAPI login failed: {data}")
                return False

        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize SmartAPI: {e}")
            return False
    
    async def _load_options_contracts(self):
        """Load available options contracts from SmartAPI (only for live mode)"""
        try:
            logger.info("[REAL] Loading real options contracts from SmartAPI...")

            # Download instrument list from SmartAPI
            instruments = await self._download_instruments()

            if not instruments:
                logger.error("[ERROR] No instruments downloaded from SmartAPI")
                return False

            # Filter NFO options contracts for NIFTY and BANKNIFTY
            options_contracts = []
            for instrument in instruments:
                if (instrument.get('exch_seg') == 'NFO' and
                    instrument.get('instrumenttype') == 'OPTIDX' and
                    instrument.get('name') in self.config.underlying_symbols):

                    # Parse strike price (divide by 100 as it's stored multiplied)
                    strike_price = float(instrument['strike']) / 100.0

                    contract = OptionsContract(
                        symbol=instrument['symbol'],
                        underlying=instrument['name'],
                        strike_price=strike_price,
                        expiry_date=instrument['expiry'],
                        option_type=instrument['symbol'][-2:],  # CE or PE
                        lot_size=int(instrument['lotsize']),
                        token=instrument['token'],
                        exchange=instrument['exch_seg']
                    )

                    options_contracts.append(contract)
                    self.contract_tokens[contract.token] = contract

            # Group by underlying
            for contract in options_contracts:
                if contract.underlying not in self.options_contracts:
                    self.options_contracts[contract.underlying] = []
                self.options_contracts[contract.underlying].append(contract)

            logger.info(f"[SUCCESS] Loaded {len(options_contracts)} real options contracts from SmartAPI")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to load options contracts: {e}")
            return False
    
    async def _download_instruments(self) -> List[Dict]:
        """Download real instrument list from SmartAPI master data"""
        try:
            logger.info("[INFO] Downloading real instruments from SmartAPI master data...")

            # Add rate limiting before downloading master data
            await api_rate_limiter.wait_if_needed()
            
            # Download master data from SmartAPI
            url = 'https://margincalculator.angelbroking.com/OpenAPI_File/files/OpenAPIScripMaster.json'
            response = requests.get(url)

            if response.status_code != 200:
                logger.error(f"[ERROR] Failed to download master data: {response.status_code}")
                return []

            all_instruments = response.json()
            logger.info(f"[INFO] Downloaded {len(all_instruments)} total instruments")

            # Filter for NFO options (NIFTY and BANKNIFTY)
            nfo_options = []
            for instrument in all_instruments:
                if (instrument.get('exch_seg') == 'NFO' and
                    instrument.get('instrumenttype') == 'OPTIDX' and
                    instrument.get('name') in ['NIFTY', 'BANKNIFTY']):

                    # Only include instruments with expiry dates specified in .env file
                    expiry_str = instrument.get('expiry')
                    if expiry_str and expiry_str != '':
                        try:
                            # Parse expiry date (format: 24DEC2025)
                            expiry_date = datetime.strptime(expiry_str, '%d%b%Y').date()
                            underlying = instrument.get('name')

                            # Check if this expiry matches the one specified in .env file
                            if underlying in self.expiry_dates_raw:
                                target_expiry_raw = self.expiry_dates_raw[underlying]
                                try:
                                    # Parse target expiry from .env (format: 14AUG2025)
                                    target_expiry_date = datetime.strptime(target_expiry_raw, '%d%b%Y').date()

                                    # Only include instruments with the exact expiry date from .env
                                    if expiry_date == target_expiry_date:
                                        nfo_options.append(instrument)
                                        logger.debug(f"[EXPIRY] Including {underlying} instrument with expiry {expiry_str} (matches .env)")
                                    else:
                                        logger.debug(f"[EXPIRY] Skipping {underlying} instrument with expiry {expiry_str} (target: {target_expiry_raw})")

                                except ValueError as ve:
                                    logger.warning(f"[WARNING] Invalid target expiry format in .env for {underlying}: {target_expiry_raw}")
                                    continue
                            else:
                                logger.debug(f"[EXPIRY] Skipping {underlying} - not configured in .env file")

                        except ValueError:
                            logger.debug(f"[DEBUG] Failed to parse expiry date: {expiry_str}")
                            continue

            # Log summary of filtered instruments by underlying and expiry
            nifty_count = sum(1 for inst in nfo_options if inst.get('name') == 'NIFTY')
            banknifty_count = sum(1 for inst in nfo_options if inst.get('name') == 'BANKNIFTY')

            logger.info(f"[SUCCESS] Filtered {len(nfo_options)} NFO options instruments")
            logger.info(f"[SUCCESS] NIFTY: {nifty_count} instruments (expiry: {self.expiry_dates_raw.get('NIFTY', 'Not set')})")
            logger.info(f"[SUCCESS] BANKNIFTY: {banknifty_count} instruments (expiry: {self.expiry_dates_raw.get('BANKNIFTY', 'Not set')})")

            if len(nfo_options) == 0:
                logger.warning("[WARNING] No instruments found matching the expiry dates in .env file!")
                logger.warning("[WARNING] Please check that NIFTY_EXPIRY and BANKNIFTY_EXPIRY in .env file match available expiry dates")

            return nfo_options

        except Exception as e:
            logger.error(f"[ERROR] Failed to download instruments: {e}")
            return []
    
    async def start(self, **kwargs) -> bool:
        """Enhanced start method with comprehensive historical data download"""
        try:
            logger.info("[START] Starting Enhanced Options Data Ingestion Agent...")

            self.is_running = True
            self.heartbeat.increment_cycle()

            # Extract date parameters from kwargs
            from_date = kwargs.get('from_date')
            to_date = kwargs.get('to_date')
            
            if from_date and to_date:
                # Check if this is live trading mode
                live_trading_mode = kwargs.get('live_trading_mode', False)

                if live_trading_mode:
                    logger.info("[MODE] Live trading mode - downloading recent historical data")

                    # Parse date strings to datetime objects
                    start_date = datetime.strptime(from_date.split(' ')[0], "%Y-%m-%d")
                    end_date = datetime.strptime(to_date.split(' ')[0], "%Y-%m-%d")

                    # Download historical data for live trading with specific strike ranges
                    success = await self._download_live_trading_historical_data(start_date, end_date, **kwargs)
                    return success
                else:
                    logger.info("[MODE] Training pipeline mode - downloading historical data")

                    # Parse date strings to datetime objects
                    start_date = datetime.strptime(from_date.split(' ')[0], "%Y-%m-%d")
                    end_date = datetime.strptime(to_date.split(' ')[0], "%Y-%m-%d")

                    # Download comprehensive historical data
                    success = await self._download_comprehensive_historical_data(start_date, end_date)
                    return success
            else:
                # Live trading mode - start real-time feeds
                logger.info("[MODE] Live trading mode - starting real-time feeds")
                
                # Start data ingestion tasks (without websocket subscription)
                tasks = [
                    self._download_recent_historical_data()
                ]

                # Run tasks concurrently
                await asyncio.gather(*tasks, return_exceptions=True)
                return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to start agent: {e}")
            return False

    async def _download_live_trading_historical_data(self, start_date: datetime, end_date: datetime, **kwargs) -> bool:
        """Download historical data specifically for live trading with focused strike ranges"""
        try:
            logger.info(f"[LIVE] Starting live trading historical data download...")
            logger.info(f"[LIVE] Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")

            # Get strike range parameters
            nifty_strike_range = kwargs.get('strike_range_nifty', 500)
            banknifty_strike_range = kwargs.get('strike_range_banknifty', 1000)

            logger.info(f"[LIVE] Strike ranges - NIFTY: ±{nifty_strike_range}, BANKNIFTY: ±{banknifty_strike_range}")

            # Step 1: Download index data to get current levels
            logger.info("[STEP 1] Downloading index data...")
            index_success = await self._download_index_data(start_date, end_date)

            if not index_success:
                logger.error("[ERROR] Failed to download index data")
                return False

            # Step 2: Calculate current strike ranges for live trading
            logger.info("[STEP 2] Calculating live trading strike ranges...")
            strike_ranges = await self._calculate_live_trading_strike_ranges(nifty_strike_range, banknifty_strike_range)

            # Step 3: Download options data for focused strike ranges
            logger.info("[STEP 3] Downloading options data for live trading...")
            options_success = await self._download_options_data_live_trading(start_date, end_date, strike_ranges)

            if not options_success:
                logger.error("[ERROR] Failed to download options data")
                return False

            # Step 4: Generate multi-timeframe data and store in data/live/
            logger.info("[STEP 4] Generating multi-timeframe data for live trading...")
            timeframe_success = await self._generate_live_timeframes()

            if not timeframe_success:
                logger.error("[ERROR] Failed to generate multi-timeframe data")
                return False

            logger.info("[SUCCESS] Live trading historical data download completed")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Live trading historical data download failed: {e}")
            return False

    async def _download_comprehensive_historical_data(self, start_date: datetime, end_date: datetime) -> bool:
        """Download comprehensive historical data for training pipeline"""
        try:
            logger.info(f"[COMPREHENSIVE] Starting comprehensive historical data download...")
            logger.info(f"[COMPREHENSIVE] Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            
            # Step 1: Download index data first
            logger.info("[STEP 1] Downloading index data...")
            index_success = await self._download_index_data(start_date, end_date)
            
            if not index_success:
                logger.error("[ERROR] Failed to download index data")
                return False
            
            # Step 2: Get high/low for the period to determine strike range
            logger.info("[STEP 2] Calculating strike price ranges...")
            strike_ranges = await self._calculate_strike_ranges(start_date, end_date)
            
            # Step 3: Download options data for calculated strike ranges
            logger.info("[STEP 3] Downloading options data...")
            options_success = await self._download_options_data_comprehensive(start_date, end_date, strike_ranges)
            
            if not options_success:
                logger.error("[ERROR] Failed to download options data")
                return False
            
            logger.info("[SUCCESS] Comprehensive historical data download completed")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Comprehensive historical data download failed: {e}")
            return False
    
    async def _download_index_data(self, start_date: datetime, end_date: datetime) -> bool:
        """Download index data for NIFTY and BANKNIFTY"""
        try:
            logger.info("[INDEX] Downloading NIFTY and BANKNIFTY index data...")
            
            # Index tokens for NIFTY and BANKNIFTY
            index_tokens = {
                'NIFTY': '********',  # NIFTY 50 token
                'BANKNIFTY': '********'  # BANK NIFTY token
            }
            
            for index_name, token in index_tokens.items():
                try:
                    logger.info(f"[INDEX] Downloading {index_name} data...")
                    
                    # Download 1-minute index data
                    historic_param = {
                        "exchange": "NSE",
                        "symboltoken": token,
                        "interval": "ONE_MINUTE",
                        "fromdate": start_date.strftime("%Y-%m-%d %H:%M"),
                        "todate": end_date.strftime("%Y-%m-%d %H:%M")
                    }
                    
                    # Add rate limiting before API call
                    await api_rate_limiter.wait_if_needed()
                    
                    # Get historical data from SmartAPI
                    hist_data = self.smart_api.getCandleData(historic_param)
                    
                    if hist_data['status'] and hist_data['data']:
                        # Convert to Polars DataFrame
                        data_list = []
                        for candle in hist_data['data']:
                            data_list.append({
                                'timestamp': datetime.strptime(candle[0], "%Y-%m-%dT%H:%M:%S%z"),
                                'open': float(candle[1]),
                                'high': float(candle[2]),
                                'low': float(candle[3]),
                                'close': float(candle[4]),
                                'volume': int(candle[5])
                            })
                        
                        if data_list:
                            df = pl.DataFrame(data_list)

                            # Save 1-minute data to historical directory
                            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                            filename = f"Index_{index_name}_1MIN_{timestamp}.parquet"
                            filepath = self.historical_path / "1min" / filename
                            df.write_parquet(filepath, compression="brotli")

                            # Also save index data for live trading in the format expected by other agents
                            await self._save_live_index_data(df, index_name, timestamp)

                            logger.info(f"[SUCCESS] Saved {len(data_list)} {index_name} 1-minute records")

                            # Generate multi-timeframe data
                            await self._generate_multi_timeframe_data(df, index_name, "Index")
                    
                    # Additional rate limiting between index downloads
                    await asyncio.sleep(0.5)
                    
                except Exception as e:
                    logger.error(f"[ERROR] Failed to download {index_name} data: {e}")
                    continue
            
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to download index data: {e}")
            return False

    async def _save_live_index_data(self, df: pl.DataFrame, index_name: str, timestamp: str):
        """Save index data in formats expected by signal generation and market monitoring agents"""
        try:
            # Create index directory for signal generation agent
            index_dir = self.live_path / "index"
            index_dir.mkdir(parents=True, exist_ok=True)

            # Save as a single consolidated JSON file for signal generation agent (more efficient)
            # Instead of creating thousands of individual files, create one file per underlying per batch
            all_ohlc_data = []
            for row in df.iter_rows(named=True):
                ohlc_data = {
                    'underlying': index_name,
                    'timestamp': row['timestamp'].isoformat() if hasattr(row['timestamp'], 'isoformat') else str(row['timestamp']),
                    'open': float(row['open']),
                    'high': float(row['high']),
                    'low': float(row['low']),
                    'close': float(row['close']),
                    'volume': int(row.get('volume', 0))
                }
                all_ohlc_data.append(ohlc_data)

            # Save all data to a single file (overwrite previous batch)
            json_filename = f"{index_name}_index_latest.json"
            json_filepath = index_dir / json_filename

            import json
            with open(json_filepath, 'w') as f:
                json.dump({
                    'batch_timestamp': timestamp,
                    'underlying': index_name,
                    'data_count': len(all_ohlc_data),
                    'data': all_ohlc_data
                }, f, indent=2)

            # Save as parquet files for market monitoring agent in each timeframe directory
            for timeframe in self.timeframes:
                timeframe_dir = self.live_path / timeframe
                timeframe_dir.mkdir(parents=True, exist_ok=True)

                # Save with the naming pattern expected by market monitoring agent
                parquet_filename = f"{index_name}_{timeframe}_{timestamp}.parquet"
                parquet_filepath = timeframe_dir / parquet_filename
                df.write_parquet(parquet_filepath, compression="brotli")

            logger.debug(f"[LIVE] Saved index data for {index_name} in multiple formats")

        except Exception as e:
            logger.error(f"[ERROR] Failed to save live index data for {index_name}: {e}")
    
    async def _calculate_strike_ranges(self, start_date: datetime, end_date: datetime) -> Dict[str, Dict]:
        """Calculate strike price ranges based on index high/low"""
        try:
            logger.info("[RANGE] Calculating strike price ranges...")
            
            strike_ranges = {}
            
            for underlying in ['NIFTY', 'BANKNIFTY']:
                try:
                    # Find the most recent index file
                    index_files = list((self.historical_path / "1min").glob(f"Index_{underlying}_1MIN_*.parquet"))
                    
                    if not index_files:
                        logger.warning(f"[WARNING] No index data found for {underlying}")
                        continue
                    
                    # Load the most recent file
                    latest_file = max(index_files, key=lambda x: x.stat().st_mtime)
                    df = pl.read_parquet(latest_file)
                    
                    # Calculate high and low for the period
                    high_price = df.select(pl.col('high').max()).item(0, 0)
                    low_price = df.select(pl.col('low').min()).item(0, 0)
                    
                    # Calculate strike range with buffer - use default values for comprehensive download
                    if underlying == 'NIFTY':
                        buffer = 500  # ±500 points for NIFTY (default for comprehensive download)
                        strike_step = 50
                    else:  # BANKNIFTY
                        buffer = 1000  # ±1000 points for BANKNIFTY (default for comprehensive download)
                        strike_step = 100
                    
                    min_strike = int((low_price - buffer) // strike_step) * strike_step
                    max_strike = int((high_price + buffer) // strike_step) * strike_step
                    
                    strike_ranges[underlying] = {
                        'min_strike': max(min_strike, 1000),  # Minimum reasonable strike
                        'max_strike': min(max_strike, 100000),  # Maximum reasonable strike
                        'high_price': high_price,
                        'low_price': low_price,
                        'buffer': buffer
                    }
                    
                    logger.info(f"[RANGE] {underlying}: {low_price:.0f} - {high_price:.0f}, "
                              f"Strike range: {strike_ranges[underlying]['min_strike']} - {strike_ranges[underlying]['max_strike']}")
                
                except Exception as e:
                    logger.error(f"[ERROR] Failed to calculate strike range for {underlying}: {e}")
                    continue
            
            return strike_ranges
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate strike ranges: {e}")
            return {}

    async def _calculate_live_trading_strike_ranges(self, nifty_range: int, banknifty_range: int) -> Dict[str, Dict]:
        """Calculate strike ranges for live trading based on current index levels"""
        try:
            logger.info("[LIVE] Calculating live trading strike ranges...")

            strike_ranges = {}

            # Get current index levels (use latest available data)
            for underlying in ['NIFTY', 'BANKNIFTY']:
                try:
                    # Find the most recent index file
                    index_files = list((self.historical_path / "1min").glob(f"Index_{underlying}_1MIN_*.parquet"))

                    if not index_files:
                        logger.warning(f"[WARNING] No index data found for {underlying}")
                        continue

                    # Load the most recent file
                    latest_file = max(index_files, key=lambda x: x.stat().st_mtime)
                    df = pl.read_parquet(latest_file)

                    # Get the latest close price
                    current_price = df.select(pl.col('close').last()).item(0, 0)

                    # Calculate strike range based on current price using passed parameters
                    if underlying == 'NIFTY':
                        buffer = nifty_range  # Use passed parameter for NIFTY
                        strike_step = 50
                    else:  # BANKNIFTY
                        buffer = banknifty_range  # Use passed parameter for BANKNIFTY
                        strike_step = 100

                    min_strike = int((current_price - buffer) // strike_step) * strike_step
                    max_strike = int((current_price + buffer) // strike_step) * strike_step

                    strike_ranges[underlying] = {
                        'min_strike': max(min_strike, 1000),  # Minimum reasonable strike
                        'max_strike': min(max_strike, 100000),  # Maximum reasonable strike
                        'current_price': current_price,
                        'buffer': buffer
                    }

                    logger.info(f"[LIVE] {underlying}: Current {current_price:.0f}, "
                              f"Strike range: {strike_ranges[underlying]['min_strike']} - {strike_ranges[underlying]['max_strike']}")

                except Exception as e:
                    logger.error(f"[ERROR] Failed to calculate strike range for {underlying}: {e}")
                    continue

            return strike_ranges

        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate live trading strike ranges: {e}")
            return {}

    async def _download_options_data_live_trading(self, start_date: datetime, end_date: datetime,
                                                strike_ranges: Dict[str, Dict]) -> bool:
        """Download options data for live trading with focused strike ranges"""
        try:
            logger.info("[LIVE] Downloading options data for live trading...")

            # Get instruments for the required strikes
            instruments = await self._download_instruments()

            if not instruments:
                logger.error("[ERROR] No instruments available")
                return False

            for underlying in ['NIFTY', 'BANKNIFTY']:
                if underlying not in strike_ranges:
                    logger.warning(f"[WARNING] No strike range calculated for {underlying}")
                    continue

                range_info = strike_ranges[underlying]
                min_strike = range_info['min_strike']
                max_strike = range_info['max_strike']

                logger.info(f"[LIVE] Downloading {underlying} options from {min_strike} to {max_strike}")

                # Filter instruments for this underlying and strike range
                relevant_instruments = []
                for instrument in instruments:
                    if (instrument.get('name') == underlying and
                        instrument.get('exch_seg') == 'NFO' and
                        instrument.get('instrumenttype') == 'OPTIDX'):

                        strike_price = float(instrument['strike']) / 100.0

                        # Additional validation to prevent cross-contamination
                        if underlying == 'NIFTY':
                            # NIFTY strikes should be in reasonable range (15000-35000) and multiples of 50
                            if not (15000 <= strike_price <= 35000 and strike_price % 50 == 0):
                                continue
                        elif underlying == 'BANKNIFTY':
                            # BANKNIFTY strikes should be in reasonable range (35000-80000) and multiples of 100
                            if not (35000 <= strike_price <= 80000 and strike_price % 100 == 0):
                                continue

                        if min_strike <= strike_price <= max_strike:
                            relevant_instruments.append(instrument)

                logger.info(f"[LIVE] Found {len(relevant_instruments)} relevant {underlying} options")

                # Use enhanced download manager for live trading (limited batch)
                limited_instruments = relevant_instruments[:50]  # Limit to 50 instruments per underlying
                await self._download_live_options_with_enhanced_manager(
                    limited_instruments, underlying, start_date, end_date
                )


            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to download live trading options data: {e}")
            return False

    async def _generate_live_timeframes(self) -> bool:
        """Generate 3min, 5min, 15min timeframes from 1min data for live trading"""
        try:
            logger.info("[LIVE] Generating multi-timeframe data from 1min data...")

            # Process all 1-minute files in live folder
            one_min_files = list((self.live_path / "1min").glob("*.parquet"))

            if not one_min_files:
                logger.warning("[WARNING] No 1-minute files found for timeframe generation")
                return True

            for file_path in one_min_files:
                try:
                    # Load 1-minute data
                    df = pl.read_parquet(file_path)

                    if df.height == 0:
                        continue

                    # Extract symbol info from filename
                    filename = file_path.stem
                    parts = filename.split('_')
                    if len(parts) >= 3:
                        underlying = parts[0]
                        strike_price = parts[1]
                        option_type = parts[2]

                        # Generate multi-timeframe data
                        await self._generate_multi_timeframe_data(df, underlying, f"{strike_price}_{option_type}")

                except Exception as e:
                    logger.error(f"[ERROR] Failed to process {file_path}: {e}")
                    continue

            logger.info("[SUCCESS] Multi-timeframe data generation completed")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate live timeframes: {e}")
            return False

    async def _download_options_data_comprehensive(self, start_date: datetime, end_date: datetime,
                                                 strike_ranges: Dict[str, Dict]) -> bool:
        """Download options data for calculated strike ranges"""
        try:
            logger.info("[OPTIONS] Downloading options data for calculated strike ranges...")
            
            # Get instruments for the required strikes
            instruments = await self._download_instruments()
            
            if not instruments:
                logger.error("[ERROR] No instruments available")
                return False
            
            for underlying in ['NIFTY', 'BANKNIFTY']:
                if underlying not in strike_ranges:
                    logger.warning(f"[WARNING] No strike range calculated for {underlying}")
                    continue
                
                range_info = strike_ranges[underlying]
                min_strike = range_info['min_strike']
                max_strike = range_info['max_strike']
                
                logger.info(f"[OPTIONS] Downloading {underlying} options from {min_strike} to {max_strike}")
                
                # Filter instruments for this underlying and strike range
                relevant_instruments = []
                for instrument in instruments:
                    if (instrument.get('name') == underlying and
                        instrument.get('exch_seg') == 'NFO' and
                        instrument.get('instrumenttype') == 'OPTIDX'):

                        strike_price = float(instrument['strike']) / 100.0

                        # Additional validation to prevent cross-contamination
                        if underlying == 'NIFTY':
                            # NIFTY strikes should be in reasonable range (15000-35000) and multiples of 50
                            if not (15000 <= strike_price <= 35000 and strike_price % 50 == 0):
                                continue
                        elif underlying == 'BANKNIFTY':
                            # BANKNIFTY strikes should be in reasonable range (35000-80000) and multiples of 100
                            if not (35000 <= strike_price <= 80000 and strike_price % 100 == 0):
                                continue

                        if min_strike <= strike_price <= max_strike:
                            relevant_instruments.append(instrument)
                
                logger.info(f"[OPTIONS] Found {len(relevant_instruments)} relevant {underlying} options")
                
                # Use enhanced download manager for parallel downloads
                await self._download_options_with_enhanced_manager(
                    relevant_instruments, underlying, start_date, end_date
                )
            
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to download options data: {e}")
            return False

    async def _download_live_options_with_enhanced_manager(self, instruments: List[Dict], underlying: str,
                                                         start_date: datetime, end_date: datetime):
        """Download live trading options data using enhanced download manager"""
        try:
            if not self.download_manager:
                logger.error("[ERROR] Enhanced download manager not initialized")
                return False

            logger.info(f"[LIVE] Starting enhanced download for {len(instruments)} {underlying} instruments")

            # Create download tasks for live trading
            download_tasks = []
            for i, instrument in enumerate(instruments):
                symbol = instrument['symbol']
                token = instrument['token']
                strike_price = float(instrument['strike']) / 100.0
                option_type = symbol[-2:]  # CE or PE

                task = DownloadTask(
                    task_id=f"live_{underlying}_{symbol}_{i}",
                    symbol=symbol,
                    token=token,
                    exchange="NFO",
                    underlying=underlying,
                    strike_price=strike_price,
                    option_type=option_type,
                    start_date=start_date.strftime("%Y-%m-%d %H:%M"),
                    end_date=end_date.strftime("%Y-%m-%d %H:%M"),
                    interval="ONE_MINUTE",
                    max_retries=2  # Fewer retries for live trading
                )
                download_tasks.append(task)

            # Add tasks and execute downloads
            self.download_manager.add_download_tasks(download_tasks)
            results = await self.download_manager.download_all_tasks()

            # Process results and save to live folder
            successful_downloads = 0
            for task_id, result in results.items():
                if result.success and result.data:
                    try:
                        # Convert and save to live folder
                        data_list = []
                        for record in result.data:
                            if isinstance(record['timestamp'], str):
                                timestamp = datetime.strptime(record['timestamp'], "%Y-%m-%dT%H:%M:%S%z")
                            else:
                                timestamp = record['timestamp']

                            data_list.append({
                                'timestamp': timestamp,
                                'open': record['open'],
                                'high': record['high'],
                                'low': record['low'],
                                'close': record['close'],
                                'volume': record['volume']
                            })

                        if data_list:
                            df = pl.DataFrame(data_list)

                            # Save to live folder
                            timestamp_str = datetime.now().strftime('%Y%m%d_%H%M%S')
                            strike_price_int = int(result.data[0]['strike_price'])
                            option_type = result.data[0]['option_type']
                            filename = f"{underlying}_{strike_price_int}_{option_type}_1MIN_{timestamp_str}.parquet"
                            filepath = self.live_path / "1min" / filename
                            df.write_parquet(filepath, compression="brotli")

                            successful_downloads += 1

                    except Exception as e:
                        logger.error(f"[ERROR] Failed to process live download {task_id}: {e}")

            logger.info(f"[LIVE] {underlying} download completed: {successful_downloads} successful")
            return successful_downloads > 0

        except Exception as e:
            logger.error(f"[ERROR] Enhanced live download failed for {underlying}: {e}")
            return False

    async def _download_options_with_enhanced_manager(self, instruments: List[Dict], underlying: str,
                                                    start_date: datetime, end_date: datetime):
        """Download options data using enhanced download manager with multiprocessing and retry"""
        try:
            if not self.download_manager:
                logger.error("[ERROR] Enhanced download manager not initialized")
                return False

            logger.info(f"[ENHANCED] Starting enhanced download for {len(instruments)} {underlying} instruments")

            # Create download tasks
            download_tasks = []
            for i, instrument in enumerate(instruments):
                symbol = instrument['symbol']
                token = instrument['token']
                strike_price = float(instrument['strike']) / 100.0
                option_type = symbol[-2:]  # CE or PE

                task = DownloadTask(
                    task_id=f"{underlying}_{symbol}_{i}",
                    symbol=symbol,
                    token=token,
                    exchange="NFO",
                    underlying=underlying,
                    strike_price=strike_price,
                    option_type=option_type,
                    start_date=start_date.strftime("%Y-%m-%d %H:%M"),
                    end_date=end_date.strftime("%Y-%m-%d %H:%M"),
                    interval="ONE_MINUTE",
                    max_retries=self.download_config['max_retries']
                )
                download_tasks.append(task)

            # Set up progress callback
            def progress_callback(completed: int, total: int, failed: int):
                if completed % 10 == 0 or completed == total:
                    success_rate = ((completed - failed) / completed * 100) if completed > 0 else 0
                    logger.info(f"[PROGRESS] {underlying}: {completed}/{total} completed, "
                              f"{failed} failed, {success_rate:.1f}% success rate")

            self.download_manager.set_progress_callback(progress_callback)

            # Add tasks to download manager
            self.download_manager.add_download_tasks(download_tasks)

            # Execute downloads
            logger.info(f"[ENHANCED] Starting parallel download of {len(download_tasks)} tasks")
            results = await self.download_manager.download_all_tasks()

            # Process results and save data
            successful_downloads = 0
            failed_downloads = 0

            for task_id, result in results.items():
                if result.success and result.data:
                    try:
                        # Convert to Polars DataFrame with timestamp parsing
                        data_list = []
                        for record in result.data:
                            # Parse timestamp if it's a string
                            if isinstance(record['timestamp'], str):
                                timestamp = datetime.strptime(record['timestamp'], "%Y-%m-%dT%H:%M:%S%z")
                            else:
                                timestamp = record['timestamp']

                            data_list.append({
                                'timestamp': timestamp,
                                'open': record['open'],
                                'high': record['high'],
                                'low': record['low'],
                                'close': record['close'],
                                'volume': record['volume'],
                                'underlying': record['underlying'],
                                'strike_price': record['strike_price'],
                                'option_type': record['option_type'],
                                'symbol': record['symbol']
                            })

                        if data_list:
                            df = pl.DataFrame(data_list)

                            # Save 1-minute data
                            timestamp_str = datetime.now().strftime('%Y%m%d_%H%M%S')
                            strike_price_int = int(data_list[0]['strike_price'])
                            option_type = data_list[0]['option_type']
                            filename = f"{underlying}_{strike_price_int}_{option_type}_1MIN_{timestamp_str}.parquet"
                            filepath = self.historical_path / "1min" / filename
                            df.write_parquet(filepath, compression="brotli")

                            logger.debug(f"[SUCCESS] Saved {len(data_list)} records for {data_list[0]['symbol']}")

                            # Generate multi-timeframe data
                            await self._generate_multi_timeframe_data(df, underlying, f"{strike_price_int}_{option_type}")

                            successful_downloads += 1

                    except Exception as e:
                        logger.error(f"[ERROR] Failed to process successful download {task_id}: {e}")
                        failed_downloads += 1
                else:
                    failed_downloads += 1
                    logger.debug(f"[FAILED] Download failed for {task_id}: {result.error}")

            # Log final statistics
            total_downloads = successful_downloads + failed_downloads
            success_rate = (successful_downloads / total_downloads * 100) if total_downloads > 0 else 0

            logger.info(f"[ENHANCED] {underlying} download completed:")
            logger.info(f"[ENHANCED] Successful: {successful_downloads}, Failed: {failed_downloads}")
            logger.info(f"[ENHANCED] Success rate: {success_rate:.1f}%")

            # Save failed tasks report if there are failures
            if failed_downloads > 0:
                failed_tasks = self.download_manager.get_failed_tasks()
                if failed_tasks:
                    report_path = self.historical_path / "download_reports"
                    report_path.mkdir(exist_ok=True)
                    self.download_manager.save_failed_tasks_report(report_path)

            return successful_downloads > 0

        except Exception as e:
            logger.error(f"[ERROR] Enhanced download failed for {underlying}: {e}")
            return False
    
    async def _generate_multi_timeframe_data(self, df_1min: pl.DataFrame, underlying: str, identifier: str):
        """Generate 3min, 5min, 15min data from 1min data using Polars"""
        try:
            # Ensure data is sorted by timestamp first (required for group_by_dynamic)
            df_1min = df_1min.sort("timestamp")

            # Define timeframe mappings
            timeframe_minutes = {
                "3min": 3,
                "5min": 5,
                "15min": 15
            }

            for timeframe, minutes in timeframe_minutes.items():
                try:
                    # Resample using Polars group_by_dynamic
                    resampled = df_1min.group_by_dynamic(
                        "timestamp",
                        every=f"{minutes}m",
                        closed="left"
                    ).agg([
                        pl.col("open").first().alias("open"),
                        pl.col("high").max().alias("high"),
                        pl.col("low").min().alias("low"),
                        pl.col("close").last().alias("close"),
                        pl.col("volume").sum().alias("volume")
                    ] + [
                        # Keep other columns if they exist
                        pl.col(col).first() for col in df_1min.columns
                        if col not in ["timestamp", "open", "high", "low", "close", "volume"]
                    ])
                    
                    if resampled.height > 0:
                        # Save resampled data to historical directory
                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                        if identifier.startswith("Index"):
                            filename = f"Index_{underlying}_{timeframe.upper()}_{timestamp}.parquet"
                        else:
                            filename = f"{underlying}_{identifier}_{timeframe.upper()}_{timestamp}.parquet"

                        filepath = self.historical_path / timeframe / filename
                        resampled.write_parquet(filepath, compression="brotli")

                        # For index data, also save to live directory for market monitoring agent
                        if identifier.startswith("Index"):
                            live_timeframe_dir = self.live_path / timeframe
                            live_timeframe_dir.mkdir(parents=True, exist_ok=True)

                            # Save with the naming pattern expected by market monitoring agent
                            live_filename = f"{underlying}_{timeframe}_{timestamp}.parquet"
                            live_filepath = live_timeframe_dir / live_filename
                            resampled.write_parquet(live_filepath, compression="brotli")

                        logger.info(f"[TIMEFRAME] Generated {resampled.height} {timeframe} records for {underlying} {identifier}")
                
                except Exception as e:
                    logger.error(f"[ERROR] Failed to generate {timeframe} data for {underlying} {identifier}: {e}")
                    raise RuntimeError(f"Critical error generating {timeframe} data for {underlying} {identifier}: {e}")
                    
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate multi-timeframe data: {e}")
    
    async def _download_recent_historical_data(self):
        """Download recent historical data for live trading preparation"""
        try:
            logger.info("[RECENT] Downloading recent historical data...")
            
            # Download last 2 days of data
            end_date = datetime.now()
            start_date = end_date - timedelta(days=2)
            
            # Use the comprehensive download method
            await self._download_comprehensive_historical_data(start_date, end_date)
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to download recent historical data: {e}")
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            logger.info("[CLEANUP] Cleaning up Options Data Ingestion Agent...")
            
            self.is_running = False
            
            # Stop heartbeat
            self.heartbeat.stop_heartbeat()
            
            logger.info("[SUCCESS] Options Data Ingestion Agent cleaned up")
            
        except Exception as e:
            logger.error(f"[ERROR] Cleanup failed: {e}")

# Example usage
async def main():
    """Example usage of Options Data Ingestion Agent"""
    agent = OptionsDataIngestionAgent()
    
    try:
        await agent.initialize()
        await agent.start()
    except KeyboardInterrupt:
        logger.info("Agent interrupted by user")
    finally:
        await agent.cleanup()

if __name__ == "__main__":
    asyncio.run(main())