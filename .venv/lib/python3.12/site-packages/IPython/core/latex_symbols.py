# encoding: utf-8

# DO NOT EDIT THIS FILE BY HAND.

# To update this file, run the script /tools/gen_latex_symbols.py using Python 3

# This file is autogenerated from the file:
# https://raw.githubusercontent.com/JuliaLang/julia/master/stdlib/REPL/src/latex_symbols.jl
# This original list is filtered to remove any unicode characters that are not valid
# Python identifiers.

latex_symbols = {
    "\\euler": "ℯ",
    "\\ohm": "Ω",
    "\\^a": "ᵃ",
    "\\^b": "ᵇ",
    "\\^c": "ᶜ",
    "\\^d": "ᵈ",
    "\\^e": "ᵉ",
    "\\^f": "ᶠ",
    "\\^g": "ᵍ",
    "\\^h": "ʰ",
    "\\^i": "ⁱ",
    "\\^j": "ʲ",
    "\\^k": "ᵏ",
    "\\^l": "ˡ",
    "\\^m": "ᵐ",
    "\\^n": "ⁿ",
    "\\^o": "ᵒ",
    "\\^p": "ᵖ",
    "\\^r": "ʳ",
    "\\^s": "ˢ",
    "\\^t": "ᵗ",
    "\\^u": "ᵘ",
    "\\^v": "ᵛ",
    "\\^w": "ʷ",
    "\\^x": "ˣ",
    "\\^y": "ʸ",
    "\\^z": "ᶻ",
    "\\^A": "ᴬ",
    "\\^B": "ᴮ",
    "\\^D": "ᴰ",
    "\\^E": "ᴱ",
    "\\^G": "ᴳ",
    "\\^H": "ᴴ",
    "\\^I": "ᴵ",
    "\\^J": "ᴶ",
    "\\^K": "ᴷ",
    "\\^L": "ᴸ",
    "\\^M": "ᴹ",
    "\\^N": "ᴺ",
    "\\^O": "ᴼ",
    "\\^P": "ᴾ",
    "\\^R": "ᴿ",
    "\\^T": "ᵀ",
    "\\^U": "ᵁ",
    "\\^V": "ⱽ",
    "\\^W": "ᵂ",
    "\\^alpha": "ᵅ",
    "\\^beta": "ᵝ",
    "\\^gamma": "ᵞ",
    "\\^delta": "ᵟ",
    "\\^epsilon": "ᵋ",
    "\\^theta": "ᶿ",
    "\\^iota": "ᶥ",
    "\\^phi": "ᵠ",
    "\\^chi": "ᵡ",
    "\\^ltphi": "ᶲ",
    "\\^uparrow": "ꜛ",
    "\\^downarrow": "ꜜ",
    "\\^!": "ꜝ",
    "\\_a": "ₐ",
    "\\_e": "ₑ",
    "\\_h": "ₕ",
    "\\_i": "ᵢ",
    "\\_j": "ⱼ",
    "\\_k": "ₖ",
    "\\_l": "ₗ",
    "\\_m": "ₘ",
    "\\_n": "ₙ",
    "\\_o": "ₒ",
    "\\_p": "ₚ",
    "\\_r": "ᵣ",
    "\\_s": "ₛ",
    "\\_t": "ₜ",
    "\\_u": "ᵤ",
    "\\_v": "ᵥ",
    "\\_x": "ₓ",
    "\\_schwa": "ₔ",
    "\\_beta": "ᵦ",
    "\\_gamma": "ᵧ",
    "\\_rho": "ᵨ",
    "\\_phi": "ᵩ",
    "\\_chi": "ᵪ",
    "\\hbar": "ħ",
    "\\sout": "̶",
    "\\ordfeminine": "ª",
    "\\cdotp": "·",
    "\\ordmasculine": "º",
    "\\AA": "Å",
    "\\AE": "Æ",
    "\\DH": "Ð",
    "\\O": "Ø",
    "\\TH": "Þ",
    "\\ss": "ß",
    "\\aa": "å",
    "\\ae": "æ",
    "\\eth": "ð",
    "\\dh": "ð",
    "\\o": "ø",
    "\\th": "þ",
    "\\DJ": "Đ",
    "\\dj": "đ",
    "\\imath": "ı",
    "\\jmath": "ȷ",
    "\\L": "Ł",
    "\\l": "ł",
    "\\NG": "Ŋ",
    "\\ng": "ŋ",
    "\\OE": "Œ",
    "\\oe": "œ",
    "\\hvlig": "ƕ",
    "\\nrleg": "ƞ",
    "\\doublepipe": "ǂ",
    "\\trna": "ɐ",
    "\\trnsa": "ɒ",
    "\\openo": "ɔ",
    "\\rtld": "ɖ",
    "\\schwa": "ə",
    "\\varepsilon": "ε",
    "\\pgamma": "ɣ",
    "\\pbgam": "ɤ",
    "\\trnh": "ɥ",
    "\\btdl": "ɬ",
    "\\rtll": "ɭ",
    "\\trnm": "ɯ",
    "\\trnmlr": "ɰ",
    "\\ltlmr": "ɱ",
    "\\ltln": "ɲ",
    "\\rtln": "ɳ",
    "\\clomeg": "ɷ",
    "\\ltphi": "ɸ",
    "\\trnr": "ɹ",
    "\\trnrl": "ɺ",
    "\\rttrnr": "ɻ",
    "\\rl": "ɼ",
    "\\rtlr": "ɽ",
    "\\fhr": "ɾ",
    "\\rtls": "ʂ",
    "\\esh": "ʃ",
    "\\trnt": "ʇ",
    "\\rtlt": "ʈ",
    "\\pupsil": "ʊ",
    "\\pscrv": "ʋ",
    "\\invv": "ʌ",
    "\\invw": "ʍ",
    "\\trny": "ʎ",
    "\\rtlz": "ʐ",
    "\\yogh": "ʒ",
    "\\glst": "ʔ",
    "\\reglst": "ʕ",
    "\\inglst": "ʖ",
    "\\turnk": "ʞ",
    "\\dyogh": "ʤ",
    "\\tesh": "ʧ",
    "\\rasp": "ʼ",
    "\\verts": "ˈ",
    "\\verti": "ˌ",
    "\\lmrk": "ː",
    "\\hlmrk": "ˑ",
    "\\grave": "̀",
    "\\acute": "́",
    "\\hat": "̂",
    "\\tilde": "̃",
    "\\bar": "̄",
    "\\breve": "̆",
    "\\dot": "̇",
    "\\ddot": "̈",
    "\\ocirc": "̊",
    "\\H": "̋",
    "\\check": "̌",
    "\\palh": "̡",
    "\\rh": "̢",
    "\\c": "̧",
    "\\k": "̨",
    "\\sbbrg": "̪",
    "\\strike": "̶",
    "\\Alpha": "Α",
    "\\Beta": "Β",
    "\\Gamma": "Γ",
    "\\Delta": "Δ",
    "\\Epsilon": "Ε",
    "\\Zeta": "Ζ",
    "\\Eta": "Η",
    "\\Theta": "Θ",
    "\\Iota": "Ι",
    "\\Kappa": "Κ",
    "\\Lambda": "Λ",
    "\\Xi": "Ξ",
    "\\Pi": "Π",
    "\\Rho": "Ρ",
    "\\Sigma": "Σ",
    "\\Tau": "Τ",
    "\\Upsilon": "Υ",
    "\\Phi": "Φ",
    "\\Chi": "Χ",
    "\\Psi": "Ψ",
    "\\Omega": "Ω",
    "\\alpha": "α",
    "\\beta": "β",
    "\\gamma": "γ",
    "\\delta": "δ",
    "\\zeta": "ζ",
    "\\eta": "η",
    "\\theta": "θ",
    "\\iota": "ι",
    "\\kappa": "κ",
    "\\lambda": "λ",
    "\\mu": "μ",
    "\\nu": "ν",
    "\\xi": "ξ",
    "\\pi": "π",
    "\\rho": "ρ",
    "\\varsigma": "ς",
    "\\sigma": "σ",
    "\\tau": "τ",
    "\\upsilon": "υ",
    "\\varphi": "φ",
    "\\chi": "χ",
    "\\psi": "ψ",
    "\\omega": "ω",
    "\\vartheta": "ϑ",
    "\\phi": "ϕ",
    "\\varpi": "ϖ",
    "\\Stigma": "Ϛ",
    "\\Digamma": "Ϝ",
    "\\digamma": "ϝ",
    "\\Koppa": "Ϟ",
    "\\Sampi": "Ϡ",
    "\\varkappa": "ϰ",
    "\\varrho": "ϱ",
    "\\varTheta": "ϴ",
    "\\epsilon": "ϵ",
    "\\dddot": "⃛",
    "\\ddddot": "⃜",
    "\\hslash": "ℏ",
    "\\Im": "ℑ",
    "\\ell": "ℓ",
    "\\wp": "℘",
    "\\Re": "ℜ",
    "\\aleph": "ℵ",
    "\\beth": "ℶ",
    "\\gimel": "ℷ",
    "\\daleth": "ℸ",
    "\\bbPi": "ℿ",
    "\\Zbar": "Ƶ",
    "\\overbar": "̅",
    "\\ovhook": "̉",
    "\\candra": "̐",
    "\\oturnedcomma": "̒",
    "\\ocommatopright": "̕",
    "\\droang": "̚",
    "\\wideutilde": "̰",
    "\\not": "̸",
    "\\Mu": "Μ",
    "\\Nu": "Ν",
    "\\Omicron": "Ο",
    "\\omicron": "ο",
    "\\varbeta": "ϐ",
    "\\oldKoppa": "Ϙ",
    "\\oldkoppa": "ϙ",
    "\\stigma": "ϛ",
    "\\koppa": "ϟ",
    "\\sampi": "ϡ",
    "\\tieconcat": "⁀",
    "\\leftharpoonaccent": "⃐",
    "\\rightharpoonaccent": "⃑",
    "\\vertoverlay": "⃒",
    "\\overleftarrow": "⃖",
    "\\vec": "⃗",
    "\\overleftrightarrow": "⃡",
    "\\annuity": "⃧",
    "\\threeunderdot": "⃨",
    "\\widebridgeabove": "⃩",
    "\\bbC": "ℂ",
    "\\eulermascheroni": "ℇ",
    "\\scrg": "ℊ",
    "\\scrH": "ℋ",
    "\\frakH": "ℌ",
    "\\bbH": "ℍ",
    "\\planck": "ℎ",
    "\\scrI": "ℐ",
    "\\scrL": "ℒ",
    "\\bbN": "ℕ",
    "\\bbP": "ℙ",
    "\\bbQ": "ℚ",
    "\\scrR": "ℛ",
    "\\bbR": "ℝ",
    "\\bbZ": "ℤ",
    "\\frakZ": "ℨ",
    "\\Angstrom": "Å",
    "\\scrB": "ℬ",
    "\\frakC": "ℭ",
    "\\scre": "ℯ",
    "\\scrE": "ℰ",
    "\\scrF": "ℱ",
    "\\Finv": "Ⅎ",
    "\\scrM": "ℳ",
    "\\scro": "ℴ",
    "\\bbgamma": "ℽ",
    "\\bbGamma": "ℾ",
    "\\bbiD": "ⅅ",
    "\\bbid": "ⅆ",
    "\\bbie": "ⅇ",
    "\\bbii": "ⅈ",
    "\\bbij": "ⅉ",
    "\\bfA": "𝐀",
    "\\bfB": "𝐁",
    "\\bfC": "𝐂",
    "\\bfD": "𝐃",
    "\\bfE": "𝐄",
    "\\bfF": "𝐅",
    "\\bfG": "𝐆",
    "\\bfH": "𝐇",
    "\\bfI": "𝐈",
    "\\bfJ": "𝐉",
    "\\bfK": "𝐊",
    "\\bfL": "𝐋",
    "\\bfM": "𝐌",
    "\\bfN": "𝐍",
    "\\bfO": "𝐎",
    "\\bfP": "𝐏",
    "\\bfQ": "𝐐",
    "\\bfR": "𝐑",
    "\\bfS": "𝐒",
    "\\bfT": "𝐓",
    "\\bfU": "𝐔",
    "\\bfV": "𝐕",
    "\\bfW": "𝐖",
    "\\bfX": "𝐗",
    "\\bfY": "𝐘",
    "\\bfZ": "𝐙",
    "\\bfa": "𝐚",
    "\\bfb": "𝐛",
    "\\bfc": "𝐜",
    "\\bfd": "𝐝",
    "\\bfe": "𝐞",
    "\\bff": "𝐟",
    "\\bfg": "𝐠",
    "\\bfh": "𝐡",
    "\\bfi": "𝐢",
    "\\bfj": "𝐣",
    "\\bfk": "𝐤",
    "\\bfl": "𝐥",
    "\\bfm": "𝐦",
    "\\bfn": "𝐧",
    "\\bfo": "𝐨",
    "\\bfp": "𝐩",
    "\\bfq": "𝐪",
    "\\bfr": "𝐫",
    "\\bfs": "𝐬",
    "\\bft": "𝐭",
    "\\bfu": "𝐮",
    "\\bfv": "𝐯",
    "\\bfw": "𝐰",
    "\\bfx": "𝐱",
    "\\bfy": "𝐲",
    "\\bfz": "𝐳",
    "\\itA": "𝐴",
    "\\itB": "𝐵",
    "\\itC": "𝐶",
    "\\itD": "𝐷",
    "\\itE": "𝐸",
    "\\itF": "𝐹",
    "\\itG": "𝐺",
    "\\itH": "𝐻",
    "\\itI": "𝐼",
    "\\itJ": "𝐽",
    "\\itK": "𝐾",
    "\\itL": "𝐿",
    "\\itM": "𝑀",
    "\\itN": "𝑁",
    "\\itO": "𝑂",
    "\\itP": "𝑃",
    "\\itQ": "𝑄",
    "\\itR": "𝑅",
    "\\itS": "𝑆",
    "\\itT": "𝑇",
    "\\itU": "𝑈",
    "\\itV": "𝑉",
    "\\itW": "𝑊",
    "\\itX": "𝑋",
    "\\itY": "𝑌",
    "\\itZ": "𝑍",
    "\\ita": "𝑎",
    "\\itb": "𝑏",
    "\\itc": "𝑐",
    "\\itd": "𝑑",
    "\\ite": "𝑒",
    "\\itf": "𝑓",
    "\\itg": "𝑔",
    "\\ith": "ℎ",
    "\\iti": "𝑖",
    "\\itj": "𝑗",
    "\\itk": "𝑘",
    "\\itl": "𝑙",
    "\\itm": "𝑚",
    "\\itn": "𝑛",
    "\\ito": "𝑜",
    "\\itp": "𝑝",
    "\\itq": "𝑞",
    "\\itr": "𝑟",
    "\\its": "𝑠",
    "\\itt": "𝑡",
    "\\itu": "𝑢",
    "\\itv": "𝑣",
    "\\itw": "𝑤",
    "\\itx": "𝑥",
    "\\ity": "𝑦",
    "\\itz": "𝑧",
    "\\biA": "𝑨",
    "\\biB": "𝑩",
    "\\biC": "𝑪",
    "\\biD": "𝑫",
    "\\biE": "𝑬",
    "\\biF": "𝑭",
    "\\biG": "𝑮",
    "\\biH": "𝑯",
    "\\biI": "𝑰",
    "\\biJ": "𝑱",
    "\\biK": "𝑲",
    "\\biL": "𝑳",
    "\\biM": "𝑴",
    "\\biN": "𝑵",
    "\\biO": "𝑶",
    "\\biP": "𝑷",
    "\\biQ": "𝑸",
    "\\biR": "𝑹",
    "\\biS": "𝑺",
    "\\biT": "𝑻",
    "\\biU": "𝑼",
    "\\biV": "𝑽",
    "\\biW": "𝑾",
    "\\biX": "𝑿",
    "\\biY": "𝒀",
    "\\biZ": "𝒁",
    "\\bia": "𝒂",
    "\\bib": "𝒃",
    "\\bic": "𝒄",
    "\\bid": "𝒅",
    "\\bie": "𝒆",
    "\\bif": "𝒇",
    "\\big": "𝒈",
    "\\bih": "𝒉",
    "\\bii": "𝒊",
    "\\bij": "𝒋",
    "\\bik": "𝒌",
    "\\bil": "𝒍",
    "\\bim": "𝒎",
    "\\bin": "𝒏",
    "\\bio": "𝒐",
    "\\bip": "𝒑",
    "\\biq": "𝒒",
    "\\bir": "𝒓",
    "\\bis": "𝒔",
    "\\bit": "𝒕",
    "\\biu": "𝒖",
    "\\biv": "𝒗",
    "\\biw": "𝒘",
    "\\bix": "𝒙",
    "\\biy": "𝒚",
    "\\biz": "𝒛",
    "\\scrA": "𝒜",
    "\\scrC": "𝒞",
    "\\scrD": "𝒟",
    "\\scrG": "𝒢",
    "\\scrJ": "𝒥",
    "\\scrK": "𝒦",
    "\\scrN": "𝒩",
    "\\scrO": "𝒪",
    "\\scrP": "𝒫",
    "\\scrQ": "𝒬",
    "\\scrS": "𝒮",
    "\\scrT": "𝒯",
    "\\scrU": "𝒰",
    "\\scrV": "𝒱",
    "\\scrW": "𝒲",
    "\\scrX": "𝒳",
    "\\scrY": "𝒴",
    "\\scrZ": "𝒵",
    "\\scra": "𝒶",
    "\\scrb": "𝒷",
    "\\scrc": "𝒸",
    "\\scrd": "𝒹",
    "\\scrf": "𝒻",
    "\\scrh": "𝒽",
    "\\scri": "𝒾",
    "\\scrj": "𝒿",
    "\\scrk": "𝓀",
    "\\scrm": "𝓂",
    "\\scrn": "𝓃",
    "\\scrp": "𝓅",
    "\\scrq": "𝓆",
    "\\scrr": "𝓇",
    "\\scrs": "𝓈",
    "\\scrt": "𝓉",
    "\\scru": "𝓊",
    "\\scrv": "𝓋",
    "\\scrw": "𝓌",
    "\\scrx": "𝓍",
    "\\scry": "𝓎",
    "\\scrz": "𝓏",
    "\\bscrA": "𝓐",
    "\\bscrB": "𝓑",
    "\\bscrC": "𝓒",
    "\\bscrD": "𝓓",
    "\\bscrE": "𝓔",
    "\\bscrF": "𝓕",
    "\\bscrG": "𝓖",
    "\\bscrH": "𝓗",
    "\\bscrI": "𝓘",
    "\\bscrJ": "𝓙",
    "\\bscrK": "𝓚",
    "\\bscrL": "𝓛",
    "\\bscrM": "𝓜",
    "\\bscrN": "𝓝",
    "\\bscrO": "𝓞",
    "\\bscrP": "𝓟",
    "\\bscrQ": "𝓠",
    "\\bscrR": "𝓡",
    "\\bscrS": "𝓢",
    "\\bscrT": "𝓣",
    "\\bscrU": "𝓤",
    "\\bscrV": "𝓥",
    "\\bscrW": "𝓦",
    "\\bscrX": "𝓧",
    "\\bscrY": "𝓨",
    "\\bscrZ": "𝓩",
    "\\bscra": "𝓪",
    "\\bscrb": "𝓫",
    "\\bscrc": "𝓬",
    "\\bscrd": "𝓭",
    "\\bscre": "𝓮",
    "\\bscrf": "𝓯",
    "\\bscrg": "𝓰",
    "\\bscrh": "𝓱",
    "\\bscri": "𝓲",
    "\\bscrj": "𝓳",
    "\\bscrk": "𝓴",
    "\\bscrl": "𝓵",
    "\\bscrm": "𝓶",
    "\\bscrn": "𝓷",
    "\\bscro": "𝓸",
    "\\bscrp": "𝓹",
    "\\bscrq": "𝓺",
    "\\bscrr": "𝓻",
    "\\bscrs": "𝓼",
    "\\bscrt": "𝓽",
    "\\bscru": "𝓾",
    "\\bscrv": "𝓿",
    "\\bscrw": "𝔀",
    "\\bscrx": "𝔁",
    "\\bscry": "𝔂",
    "\\bscrz": "𝔃",
    "\\frakA": "𝔄",
    "\\frakB": "𝔅",
    "\\frakD": "𝔇",
    "\\frakE": "𝔈",
    "\\frakF": "𝔉",
    "\\frakG": "𝔊",
    "\\frakI": "ℑ",
    "\\frakJ": "𝔍",
    "\\frakK": "𝔎",
    "\\frakL": "𝔏",
    "\\frakM": "𝔐",
    "\\frakN": "𝔑",
    "\\frakO": "𝔒",
    "\\frakP": "𝔓",
    "\\frakQ": "𝔔",
    "\\frakR": "ℜ",
    "\\frakS": "𝔖",
    "\\frakT": "𝔗",
    "\\frakU": "𝔘",
    "\\frakV": "𝔙",
    "\\frakW": "𝔚",
    "\\frakX": "𝔛",
    "\\frakY": "𝔜",
    "\\fraka": "𝔞",
    "\\frakb": "𝔟",
    "\\frakc": "𝔠",
    "\\frakd": "𝔡",
    "\\frake": "𝔢",
    "\\frakf": "𝔣",
    "\\frakg": "𝔤",
    "\\frakh": "𝔥",
    "\\fraki": "𝔦",
    "\\frakj": "𝔧",
    "\\frakk": "𝔨",
    "\\frakl": "𝔩",
    "\\frakm": "𝔪",
    "\\frakn": "𝔫",
    "\\frako": "𝔬",
    "\\frakp": "𝔭",
    "\\frakq": "𝔮",
    "\\frakr": "𝔯",
    "\\fraks": "𝔰",
    "\\frakt": "𝔱",
    "\\fraku": "𝔲",
    "\\frakv": "𝔳",
    "\\frakw": "𝔴",
    "\\frakx": "𝔵",
    "\\fraky": "𝔶",
    "\\frakz": "𝔷",
    "\\bbA": "𝔸",
    "\\bbB": "𝔹",
    "\\bbD": "𝔻",
    "\\bbE": "𝔼",
    "\\bbF": "𝔽",
    "\\bbG": "𝔾",
    "\\bbI": "𝕀",
    "\\bbJ": "𝕁",
    "\\bbK": "𝕂",
    "\\bbL": "𝕃",
    "\\bbM": "𝕄",
    "\\bbO": "𝕆",
    "\\bbS": "𝕊",
    "\\bbT": "𝕋",
    "\\bbU": "𝕌",
    "\\bbV": "𝕍",
    "\\bbW": "𝕎",
    "\\bbX": "𝕏",
    "\\bbY": "𝕐",
    "\\bba": "𝕒",
    "\\bbb": "𝕓",
    "\\bbc": "𝕔",
    "\\bbd": "𝕕",
    "\\bbe": "𝕖",
    "\\bbf": "𝕗",
    "\\bbg": "𝕘",
    "\\bbh": "𝕙",
    "\\bbi": "𝕚",
    "\\bbj": "𝕛",
    "\\bbk": "𝕜",
    "\\bbl": "𝕝",
    "\\bbm": "𝕞",
    "\\bbn": "𝕟",
    "\\bbo": "𝕠",
    "\\bbp": "𝕡",
    "\\bbq": "𝕢",
    "\\bbr": "𝕣",
    "\\bbs": "𝕤",
    "\\bbt": "𝕥",
    "\\bbu": "𝕦",
    "\\bbv": "𝕧",
    "\\bbw": "𝕨",
    "\\bbx": "𝕩",
    "\\bby": "𝕪",
    "\\bbz": "𝕫",
    "\\bfrakA": "𝕬",
    "\\bfrakB": "𝕭",
    "\\bfrakC": "𝕮",
    "\\bfrakD": "𝕯",
    "\\bfrakE": "𝕰",
    "\\bfrakF": "𝕱",
    "\\bfrakG": "𝕲",
    "\\bfrakH": "𝕳",
    "\\bfrakI": "𝕴",
    "\\bfrakJ": "𝕵",
    "\\bfrakK": "𝕶",
    "\\bfrakL": "𝕷",
    "\\bfrakM": "𝕸",
    "\\bfrakN": "𝕹",
    "\\bfrakO": "𝕺",
    "\\bfrakP": "𝕻",
    "\\bfrakQ": "𝕼",
    "\\bfrakR": "𝕽",
    "\\bfrakS": "𝕾",
    "\\bfrakT": "𝕿",
    "\\bfrakU": "𝖀",
    "\\bfrakV": "𝖁",
    "\\bfrakW": "𝖂",
    "\\bfrakX": "𝖃",
    "\\bfrakY": "𝖄",
    "\\bfrakZ": "𝖅",
    "\\bfraka": "𝖆",
    "\\bfrakb": "𝖇",
    "\\bfrakc": "𝖈",
    "\\bfrakd": "𝖉",
    "\\bfrake": "𝖊",
    "\\bfrakf": "𝖋",
    "\\bfrakg": "𝖌",
    "\\bfrakh": "𝖍",
    "\\bfraki": "𝖎",
    "\\bfrakj": "𝖏",
    "\\bfrakk": "𝖐",
    "\\bfrakl": "𝖑",
    "\\bfrakm": "𝖒",
    "\\bfrakn": "𝖓",
    "\\bfrako": "𝖔",
    "\\bfrakp": "𝖕",
    "\\bfrakq": "𝖖",
    "\\bfrakr": "𝖗",
    "\\bfraks": "𝖘",
    "\\bfrakt": "𝖙",
    "\\bfraku": "𝖚",
    "\\bfrakv": "𝖛",
    "\\bfrakw": "𝖜",
    "\\bfrakx": "𝖝",
    "\\bfraky": "𝖞",
    "\\bfrakz": "𝖟",
    "\\sansA": "𝖠",
    "\\sansB": "𝖡",
    "\\sansC": "𝖢",
    "\\sansD": "𝖣",
    "\\sansE": "𝖤",
    "\\sansF": "𝖥",
    "\\sansG": "𝖦",
    "\\sansH": "𝖧",
    "\\sansI": "𝖨",
    "\\sansJ": "𝖩",
    "\\sansK": "𝖪",
    "\\sansL": "𝖫",
    "\\sansM": "𝖬",
    "\\sansN": "𝖭",
    "\\sansO": "𝖮",
    "\\sansP": "𝖯",
    "\\sansQ": "𝖰",
    "\\sansR": "𝖱",
    "\\sansS": "𝖲",
    "\\sansT": "𝖳",
    "\\sansU": "𝖴",
    "\\sansV": "𝖵",
    "\\sansW": "𝖶",
    "\\sansX": "𝖷",
    "\\sansY": "𝖸",
    "\\sansZ": "𝖹",
    "\\sansa": "𝖺",
    "\\sansb": "𝖻",
    "\\sansc": "𝖼",
    "\\sansd": "𝖽",
    "\\sanse": "𝖾",
    "\\sansf": "𝖿",
    "\\sansg": "𝗀",
    "\\sansh": "𝗁",
    "\\sansi": "𝗂",
    "\\sansj": "𝗃",
    "\\sansk": "𝗄",
    "\\sansl": "𝗅",
    "\\sansm": "𝗆",
    "\\sansn": "𝗇",
    "\\sanso": "𝗈",
    "\\sansp": "𝗉",
    "\\sansq": "𝗊",
    "\\sansr": "𝗋",
    "\\sanss": "𝗌",
    "\\sanst": "𝗍",
    "\\sansu": "𝗎",
    "\\sansv": "𝗏",
    "\\sansw": "𝗐",
    "\\sansx": "𝗑",
    "\\sansy": "𝗒",
    "\\sansz": "𝗓",
    "\\bsansA": "𝗔",
    "\\bsansB": "𝗕",
    "\\bsansC": "𝗖",
    "\\bsansD": "𝗗",
    "\\bsansE": "𝗘",
    "\\bsansF": "𝗙",
    "\\bsansG": "𝗚",
    "\\bsansH": "𝗛",
    "\\bsansI": "𝗜",
    "\\bsansJ": "𝗝",
    "\\bsansK": "𝗞",
    "\\bsansL": "𝗟",
    "\\bsansM": "𝗠",
    "\\bsansN": "𝗡",
    "\\bsansO": "𝗢",
    "\\bsansP": "𝗣",
    "\\bsansQ": "𝗤",
    "\\bsansR": "𝗥",
    "\\bsansS": "𝗦",
    "\\bsansT": "𝗧",
    "\\bsansU": "𝗨",
    "\\bsansV": "𝗩",
    "\\bsansW": "𝗪",
    "\\bsansX": "𝗫",
    "\\bsansY": "𝗬",
    "\\bsansZ": "𝗭",
    "\\bsansa": "𝗮",
    "\\bsansb": "𝗯",
    "\\bsansc": "𝗰",
    "\\bsansd": "𝗱",
    "\\bsanse": "𝗲",
    "\\bsansf": "𝗳",
    "\\bsansg": "𝗴",
    "\\bsansh": "𝗵",
    "\\bsansi": "𝗶",
    "\\bsansj": "𝗷",
    "\\bsansk": "𝗸",
    "\\bsansl": "𝗹",
    "\\bsansm": "𝗺",
    "\\bsansn": "𝗻",
    "\\bsanso": "𝗼",
    "\\bsansp": "𝗽",
    "\\bsansq": "𝗾",
    "\\bsansr": "𝗿",
    "\\bsanss": "𝘀",
    "\\bsanst": "𝘁",
    "\\bsansu": "𝘂",
    "\\bsansv": "𝘃",
    "\\bsansw": "𝘄",
    "\\bsansx": "𝘅",
    "\\bsansy": "𝘆",
    "\\bsansz": "𝘇",
    "\\isansA": "𝘈",
    "\\isansB": "𝘉",
    "\\isansC": "𝘊",
    "\\isansD": "𝘋",
    "\\isansE": "𝘌",
    "\\isansF": "𝘍",
    "\\isansG": "𝘎",
    "\\isansH": "𝘏",
    "\\isansI": "𝘐",
    "\\isansJ": "𝘑",
    "\\isansK": "𝘒",
    "\\isansL": "𝘓",
    "\\isansM": "𝘔",
    "\\isansN": "𝘕",
    "\\isansO": "𝘖",
    "\\isansP": "𝘗",
    "\\isansQ": "𝘘",
    "\\isansR": "𝘙",
    "\\isansS": "𝘚",
    "\\isansT": "𝘛",
    "\\isansU": "𝘜",
    "\\isansV": "𝘝",
    "\\isansW": "𝘞",
    "\\isansX": "𝘟",
    "\\isansY": "𝘠",
    "\\isansZ": "𝘡",
    "\\isansa": "𝘢",
    "\\isansb": "𝘣",
    "\\isansc": "𝘤",
    "\\isansd": "𝘥",
    "\\isanse": "𝘦",
    "\\isansf": "𝘧",
    "\\isansg": "𝘨",
    "\\isansh": "𝘩",
    "\\isansi": "𝘪",
    "\\isansj": "𝘫",
    "\\isansk": "𝘬",
    "\\isansl": "𝘭",
    "\\isansm": "𝘮",
    "\\isansn": "𝘯",
    "\\isanso": "𝘰",
    "\\isansp": "𝘱",
    "\\isansq": "𝘲",
    "\\isansr": "𝘳",
    "\\isanss": "𝘴",
    "\\isanst": "𝘵",
    "\\isansu": "𝘶",
    "\\isansv": "𝘷",
    "\\isansw": "𝘸",
    "\\isansx": "𝘹",
    "\\isansy": "𝘺",
    "\\isansz": "𝘻",
    "\\bisansA": "𝘼",
    "\\bisansB": "𝘽",
    "\\bisansC": "𝘾",
    "\\bisansD": "𝘿",
    "\\bisansE": "𝙀",
    "\\bisansF": "𝙁",
    "\\bisansG": "𝙂",
    "\\bisansH": "𝙃",
    "\\bisansI": "𝙄",
    "\\bisansJ": "𝙅",
    "\\bisansK": "𝙆",
    "\\bisansL": "𝙇",
    "\\bisansM": "𝙈",
    "\\bisansN": "𝙉",
    "\\bisansO": "𝙊",
    "\\bisansP": "𝙋",
    "\\bisansQ": "𝙌",
    "\\bisansR": "𝙍",
    "\\bisansS": "𝙎",
    "\\bisansT": "𝙏",
    "\\bisansU": "𝙐",
    "\\bisansV": "𝙑",
    "\\bisansW": "𝙒",
    "\\bisansX": "𝙓",
    "\\bisansY": "𝙔",
    "\\bisansZ": "𝙕",
    "\\bisansa": "𝙖",
    "\\bisansb": "𝙗",
    "\\bisansc": "𝙘",
    "\\bisansd": "𝙙",
    "\\bisanse": "𝙚",
    "\\bisansf": "𝙛",
    "\\bisansg": "𝙜",
    "\\bisansh": "𝙝",
    "\\bisansi": "𝙞",
    "\\bisansj": "𝙟",
    "\\bisansk": "𝙠",
    "\\bisansl": "𝙡",
    "\\bisansm": "𝙢",
    "\\bisansn": "𝙣",
    "\\bisanso": "𝙤",
    "\\bisansp": "𝙥",
    "\\bisansq": "𝙦",
    "\\bisansr": "𝙧",
    "\\bisanss": "𝙨",
    "\\bisanst": "𝙩",
    "\\bisansu": "𝙪",
    "\\bisansv": "𝙫",
    "\\bisansw": "𝙬",
    "\\bisansx": "𝙭",
    "\\bisansy": "𝙮",
    "\\bisansz": "𝙯",
    "\\ttA": "𝙰",
    "\\ttB": "𝙱",
    "\\ttC": "𝙲",
    "\\ttD": "𝙳",
    "\\ttE": "𝙴",
    "\\ttF": "𝙵",
    "\\ttG": "𝙶",
    "\\ttH": "𝙷",
    "\\ttI": "𝙸",
    "\\ttJ": "𝙹",
    "\\ttK": "𝙺",
    "\\ttL": "𝙻",
    "\\ttM": "𝙼",
    "\\ttN": "𝙽",
    "\\ttO": "𝙾",
    "\\ttP": "𝙿",
    "\\ttQ": "𝚀",
    "\\ttR": "𝚁",
    "\\ttS": "𝚂",
    "\\ttT": "𝚃",
    "\\ttU": "𝚄",
    "\\ttV": "𝚅",
    "\\ttW": "𝚆",
    "\\ttX": "𝚇",
    "\\ttY": "𝚈",
    "\\ttZ": "𝚉",
    "\\tta": "𝚊",
    "\\ttb": "𝚋",
    "\\ttc": "𝚌",
    "\\ttd": "𝚍",
    "\\tte": "𝚎",
    "\\ttf": "𝚏",
    "\\ttg": "𝚐",
    "\\tth": "𝚑",
    "\\tti": "𝚒",
    "\\ttj": "𝚓",
    "\\ttk": "𝚔",
    "\\ttl": "𝚕",
    "\\ttm": "𝚖",
    "\\ttn": "𝚗",
    "\\tto": "𝚘",
    "\\ttp": "𝚙",
    "\\ttq": "𝚚",
    "\\ttr": "𝚛",
    "\\tts": "𝚜",
    "\\ttt": "𝚝",
    "\\ttu": "𝚞",
    "\\ttv": "𝚟",
    "\\ttw": "𝚠",
    "\\ttx": "𝚡",
    "\\tty": "𝚢",
    "\\ttz": "𝚣",
    "\\bfAlpha": "𝚨",
    "\\bfBeta": "𝚩",
    "\\bfGamma": "𝚪",
    "\\bfDelta": "𝚫",
    "\\bfEpsilon": "𝚬",
    "\\bfZeta": "𝚭",
    "\\bfEta": "𝚮",
    "\\bfTheta": "𝚯",
    "\\bfIota": "𝚰",
    "\\bfKappa": "𝚱",
    "\\bfLambda": "𝚲",
    "\\bfMu": "𝚳",
    "\\bfNu": "𝚴",
    "\\bfXi": "𝚵",
    "\\bfOmicron": "𝚶",
    "\\bfPi": "𝚷",
    "\\bfRho": "𝚸",
    "\\bfvarTheta": "𝚹",
    "\\bfSigma": "𝚺",
    "\\bfTau": "𝚻",
    "\\bfUpsilon": "𝚼",
    "\\bfPhi": "𝚽",
    "\\bfChi": "𝚾",
    "\\bfPsi": "𝚿",
    "\\bfOmega": "𝛀",
    "\\bfalpha": "𝛂",
    "\\bfbeta": "𝛃",
    "\\bfgamma": "𝛄",
    "\\bfdelta": "𝛅",
    "\\bfvarepsilon": "𝛆",
    "\\bfzeta": "𝛇",
    "\\bfeta": "𝛈",
    "\\bftheta": "𝛉",
    "\\bfiota": "𝛊",
    "\\bfkappa": "𝛋",
    "\\bflambda": "𝛌",
    "\\bfmu": "𝛍",
    "\\bfnu": "𝛎",
    "\\bfxi": "𝛏",
    "\\bfomicron": "𝛐",
    "\\bfpi": "𝛑",
    "\\bfrho": "𝛒",
    "\\bfvarsigma": "𝛓",
    "\\bfsigma": "𝛔",
    "\\bftau": "𝛕",
    "\\bfupsilon": "𝛖",
    "\\bfvarphi": "𝛗",
    "\\bfchi": "𝛘",
    "\\bfpsi": "𝛙",
    "\\bfomega": "𝛚",
    "\\bfepsilon": "𝛜",
    "\\bfvartheta": "𝛝",
    "\\bfvarkappa": "𝛞",
    "\\bfphi": "𝛟",
    "\\bfvarrho": "𝛠",
    "\\bfvarpi": "𝛡",
    "\\itAlpha": "𝛢",
    "\\itBeta": "𝛣",
    "\\itGamma": "𝛤",
    "\\itDelta": "𝛥",
    "\\itEpsilon": "𝛦",
    "\\itZeta": "𝛧",
    "\\itEta": "𝛨",
    "\\itTheta": "𝛩",
    "\\itIota": "𝛪",
    "\\itKappa": "𝛫",
    "\\itLambda": "𝛬",
    "\\itMu": "𝛭",
    "\\itNu": "𝛮",
    "\\itXi": "𝛯",
    "\\itOmicron": "𝛰",
    "\\itPi": "𝛱",
    "\\itRho": "𝛲",
    "\\itvarTheta": "𝛳",
    "\\itSigma": "𝛴",
    "\\itTau": "𝛵",
    "\\itUpsilon": "𝛶",
    "\\itPhi": "𝛷",
    "\\itChi": "𝛸",
    "\\itPsi": "𝛹",
    "\\itOmega": "𝛺",
    "\\italpha": "𝛼",
    "\\itbeta": "𝛽",
    "\\itgamma": "𝛾",
    "\\itdelta": "𝛿",
    "\\itvarepsilon": "𝜀",
    "\\itzeta": "𝜁",
    "\\iteta": "𝜂",
    "\\ittheta": "𝜃",
    "\\itiota": "𝜄",
    "\\itkappa": "𝜅",
    "\\itlambda": "𝜆",
    "\\itmu": "𝜇",
    "\\itnu": "𝜈",
    "\\itxi": "𝜉",
    "\\itomicron": "𝜊",
    "\\itpi": "𝜋",
    "\\itrho": "𝜌",
    "\\itvarsigma": "𝜍",
    "\\itsigma": "𝜎",
    "\\ittau": "𝜏",
    "\\itupsilon": "𝜐",
    "\\itvarphi": "𝜑",
    "\\itchi": "𝜒",
    "\\itpsi": "𝜓",
    "\\itomega": "𝜔",
    "\\itepsilon": "𝜖",
    "\\itvartheta": "𝜗",
    "\\itvarkappa": "𝜘",
    "\\itphi": "𝜙",
    "\\itvarrho": "𝜚",
    "\\itvarpi": "𝜛",
    "\\biAlpha": "𝜜",
    "\\biBeta": "𝜝",
    "\\biGamma": "𝜞",
    "\\biDelta": "𝜟",
    "\\biEpsilon": "𝜠",
    "\\biZeta": "𝜡",
    "\\biEta": "𝜢",
    "\\biTheta": "𝜣",
    "\\biIota": "𝜤",
    "\\biKappa": "𝜥",
    "\\biLambda": "𝜦",
    "\\biMu": "𝜧",
    "\\biNu": "𝜨",
    "\\biXi": "𝜩",
    "\\biOmicron": "𝜪",
    "\\biPi": "𝜫",
    "\\biRho": "𝜬",
    "\\bivarTheta": "𝜭",
    "\\biSigma": "𝜮",
    "\\biTau": "𝜯",
    "\\biUpsilon": "𝜰",
    "\\biPhi": "𝜱",
    "\\biChi": "𝜲",
    "\\biPsi": "𝜳",
    "\\biOmega": "𝜴",
    "\\bialpha": "𝜶",
    "\\bibeta": "𝜷",
    "\\bigamma": "𝜸",
    "\\bidelta": "𝜹",
    "\\bivarepsilon": "𝜺",
    "\\bizeta": "𝜻",
    "\\bieta": "𝜼",
    "\\bitheta": "𝜽",
    "\\biiota": "𝜾",
    "\\bikappa": "𝜿",
    "\\bilambda": "𝝀",
    "\\bimu": "𝝁",
    "\\binu": "𝝂",
    "\\bixi": "𝝃",
    "\\biomicron": "𝝄",
    "\\bipi": "𝝅",
    "\\birho": "𝝆",
    "\\bivarsigma": "𝝇",
    "\\bisigma": "𝝈",
    "\\bitau": "𝝉",
    "\\biupsilon": "𝝊",
    "\\bivarphi": "𝝋",
    "\\bichi": "𝝌",
    "\\bipsi": "𝝍",
    "\\biomega": "𝝎",
    "\\biepsilon": "𝝐",
    "\\bivartheta": "𝝑",
    "\\bivarkappa": "𝝒",
    "\\biphi": "𝝓",
    "\\bivarrho": "𝝔",
    "\\bivarpi": "𝝕",
    "\\bsansAlpha": "𝝖",
    "\\bsansBeta": "𝝗",
    "\\bsansGamma": "𝝘",
    "\\bsansDelta": "𝝙",
    "\\bsansEpsilon": "𝝚",
    "\\bsansZeta": "𝝛",
    "\\bsansEta": "𝝜",
    "\\bsansTheta": "𝝝",
    "\\bsansIota": "𝝞",
    "\\bsansKappa": "𝝟",
    "\\bsansLambda": "𝝠",
    "\\bsansMu": "𝝡",
    "\\bsansNu": "𝝢",
    "\\bsansXi": "𝝣",
    "\\bsansOmicron": "𝝤",
    "\\bsansPi": "𝝥",
    "\\bsansRho": "𝝦",
    "\\bsansvarTheta": "𝝧",
    "\\bsansSigma": "𝝨",
    "\\bsansTau": "𝝩",
    "\\bsansUpsilon": "𝝪",
    "\\bsansPhi": "𝝫",
    "\\bsansChi": "𝝬",
    "\\bsansPsi": "𝝭",
    "\\bsansOmega": "𝝮",
    "\\bsansalpha": "𝝰",
    "\\bsansbeta": "𝝱",
    "\\bsansgamma": "𝝲",
    "\\bsansdelta": "𝝳",
    "\\bsansvarepsilon": "𝝴",
    "\\bsanszeta": "𝝵",
    "\\bsanseta": "𝝶",
    "\\bsanstheta": "𝝷",
    "\\bsansiota": "𝝸",
    "\\bsanskappa": "𝝹",
    "\\bsanslambda": "𝝺",
    "\\bsansmu": "𝝻",
    "\\bsansnu": "𝝼",
    "\\bsansxi": "𝝽",
    "\\bsansomicron": "𝝾",
    "\\bsanspi": "𝝿",
    "\\bsansrho": "𝞀",
    "\\bsansvarsigma": "𝞁",
    "\\bsanssigma": "𝞂",
    "\\bsanstau": "𝞃",
    "\\bsansupsilon": "𝞄",
    "\\bsansvarphi": "𝞅",
    "\\bsanschi": "𝞆",
    "\\bsanspsi": "𝞇",
    "\\bsansomega": "𝞈",
    "\\bsansepsilon": "𝞊",
    "\\bsansvartheta": "𝞋",
    "\\bsansvarkappa": "𝞌",
    "\\bsansphi": "𝞍",
    "\\bsansvarrho": "𝞎",
    "\\bsansvarpi": "𝞏",
    "\\bisansAlpha": "𝞐",
    "\\bisansBeta": "𝞑",
    "\\bisansGamma": "𝞒",
    "\\bisansDelta": "𝞓",
    "\\bisansEpsilon": "𝞔",
    "\\bisansZeta": "𝞕",
    "\\bisansEta": "𝞖",
    "\\bisansTheta": "𝞗",
    "\\bisansIota": "𝞘",
    "\\bisansKappa": "𝞙",
    "\\bisansLambda": "𝞚",
    "\\bisansMu": "𝞛",
    "\\bisansNu": "𝞜",
    "\\bisansXi": "𝞝",
    "\\bisansOmicron": "𝞞",
    "\\bisansPi": "𝞟",
    "\\bisansRho": "𝞠",
    "\\bisansvarTheta": "𝞡",
    "\\bisansSigma": "𝞢",
    "\\bisansTau": "𝞣",
    "\\bisansUpsilon": "𝞤",
    "\\bisansPhi": "𝞥",
    "\\bisansChi": "𝞦",
    "\\bisansPsi": "𝞧",
    "\\bisansOmega": "𝞨",
    "\\bisansalpha": "𝞪",
    "\\bisansbeta": "𝞫",
    "\\bisansgamma": "𝞬",
    "\\bisansdelta": "𝞭",
    "\\bisansvarepsilon": "𝞮",
    "\\bisanszeta": "𝞯",
    "\\bisanseta": "𝞰",
    "\\bisanstheta": "𝞱",
    "\\bisansiota": "𝞲",
    "\\bisanskappa": "𝞳",
    "\\bisanslambda": "𝞴",
    "\\bisansmu": "𝞵",
    "\\bisansnu": "𝞶",
    "\\bisansxi": "𝞷",
    "\\bisansomicron": "𝞸",
    "\\bisanspi": "𝞹",
    "\\bisansrho": "𝞺",
    "\\bisansvarsigma": "𝞻",
    "\\bisanssigma": "𝞼",
    "\\bisanstau": "𝞽",
    "\\bisansupsilon": "𝞾",
    "\\bisansvarphi": "𝞿",
    "\\bisanschi": "𝟀",
    "\\bisanspsi": "𝟁",
    "\\bisansomega": "𝟂",
    "\\bisansepsilon": "𝟄",
    "\\bisansvartheta": "𝟅",
    "\\bisansvarkappa": "𝟆",
    "\\bisansphi": "𝟇",
    "\\bisansvarrho": "𝟈",
    "\\bisansvarpi": "𝟉",
    "\\bfzero": "𝟎",
    "\\bfone": "𝟏",
    "\\bftwo": "𝟐",
    "\\bfthree": "𝟑",
    "\\bffour": "𝟒",
    "\\bffive": "𝟓",
    "\\bfsix": "𝟔",
    "\\bfseven": "𝟕",
    "\\bfeight": "𝟖",
    "\\bfnine": "𝟗",
    "\\bbzero": "𝟘",
    "\\bbone": "𝟙",
    "\\bbtwo": "𝟚",
    "\\bbthree": "𝟛",
    "\\bbfour": "𝟜",
    "\\bbfive": "𝟝",
    "\\bbsix": "𝟞",
    "\\bbseven": "𝟟",
    "\\bbeight": "𝟠",
    "\\bbnine": "𝟡",
    "\\sanszero": "𝟢",
    "\\sansone": "𝟣",
    "\\sanstwo": "𝟤",
    "\\sansthree": "𝟥",
    "\\sansfour": "𝟦",
    "\\sansfive": "𝟧",
    "\\sanssix": "𝟨",
    "\\sansseven": "𝟩",
    "\\sanseight": "𝟪",
    "\\sansnine": "𝟫",
    "\\bsanszero": "𝟬",
    "\\bsansone": "𝟭",
    "\\bsanstwo": "𝟮",
    "\\bsansthree": "𝟯",
    "\\bsansfour": "𝟰",
    "\\bsansfive": "𝟱",
    "\\bsanssix": "𝟲",
    "\\bsansseven": "𝟳",
    "\\bsanseight": "𝟴",
    "\\bsansnine": "𝟵",
    "\\ttzero": "𝟶",
    "\\ttone": "𝟷",
    "\\tttwo": "𝟸",
    "\\ttthree": "𝟹",
    "\\ttfour": "𝟺",
    "\\ttfive": "𝟻",
    "\\ttsix": "𝟼",
    "\\ttseven": "𝟽",
    "\\tteight": "𝟾",
    "\\ttnine": "𝟿",
    "\\underbar": "̲",
    "\\underleftrightarrow": "͍",
}


reverse_latex_symbol = {v: k for k, v in latex_symbols.items()}
