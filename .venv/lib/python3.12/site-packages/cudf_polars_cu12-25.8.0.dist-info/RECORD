cudf_polars/VERSION,sha256=mZz9G1Ul4kEOksaMu07UE-AVtGzT1t91nQu3CK9KUAk,8
cudf_polars/__init__.py,sha256=fSTx5nmqajdwp7qvP4PnYL6wZN9-k1fKB43NkcZlHwk,740
cudf_polars/__pycache__/__init__.cpython-312.pyc,,
cudf_polars/__pycache__/_version.cpython-312.pyc,,
cudf_polars/__pycache__/callback.cpython-312.pyc,,
cudf_polars/_version.py,sha256=kj5Ir4dxZRR-k2k8mWUDJHiGpE8_ZcTNzt_kMZxcFRA,528
cudf_polars/callback.py,sha256=v2CJVFbQQbgs5y41sH7hFJ07Z51gZpjR9m00ifELl_s,10190
cudf_polars/containers/__init__.py,sha256=EIiTQKXBTwCmbsUNotYImSi3wq31pX55hYFwojygcyI,518
cudf_polars/containers/__pycache__/__init__.cpython-312.pyc,,
cudf_polars/containers/__pycache__/column.cpython-312.pyc,,
cudf_polars/containers/__pycache__/dataframe.cpython-312.pyc,,
cudf_polars/containers/__pycache__/datatype.cpython-312.pyc,,
cudf_polars/containers/column.py,sha256=H-PaD5AB68JojDGeAKMPMRbfiZOyMfmd7wJdCn1kI54,14105
cudf_polars/containers/dataframe.py,sha256=UZBQAyzycP4qjoVlFC8AMJIetCpeWeMkyKzyYe7FpvY,11464
cudf_polars/containers/datatype.py,sha256=DOFgvSgYmP6XphlOAIUeoFDyjtEwSl9TQOsmm8JSF_c,4569
cudf_polars/dsl/__init__.py,sha256=bYwYnqmqINMgwnkJ22EnXMlHviLolPaMgQ8QqoZL3YE,244
cudf_polars/dsl/__pycache__/__init__.cpython-312.pyc,,
cudf_polars/dsl/__pycache__/expr.cpython-312.pyc,,
cudf_polars/dsl/__pycache__/ir.cpython-312.pyc,,
cudf_polars/dsl/__pycache__/nodebase.cpython-312.pyc,,
cudf_polars/dsl/__pycache__/to_ast.cpython-312.pyc,,
cudf_polars/dsl/__pycache__/tracing.cpython-312.pyc,,
cudf_polars/dsl/__pycache__/translate.cpython-312.pyc,,
cudf_polars/dsl/__pycache__/traversal.cpython-312.pyc,,
cudf_polars/dsl/expr.py,sha256=fLimKmIxdEkVwie90QR3ajjxA7le5zI-iu1VZW1ZN8c,1952
cudf_polars/dsl/expressions/__init__.py,sha256=uj1a4BzrDVAjOgP6FKKtnvR5elF4ksUj1RhkLTZoS1k,224
cudf_polars/dsl/expressions/__pycache__/__init__.cpython-312.pyc,,
cudf_polars/dsl/expressions/__pycache__/aggregation.cpython-312.pyc,,
cudf_polars/dsl/expressions/__pycache__/base.cpython-312.pyc,,
cudf_polars/dsl/expressions/__pycache__/binaryop.cpython-312.pyc,,
cudf_polars/dsl/expressions/__pycache__/boolean.cpython-312.pyc,,
cudf_polars/dsl/expressions/__pycache__/datetime.cpython-312.pyc,,
cudf_polars/dsl/expressions/__pycache__/literal.cpython-312.pyc,,
cudf_polars/dsl/expressions/__pycache__/rolling.cpython-312.pyc,,
cudf_polars/dsl/expressions/__pycache__/selection.cpython-312.pyc,,
cudf_polars/dsl/expressions/__pycache__/slicing.cpython-312.pyc,,
cudf_polars/dsl/expressions/__pycache__/sorting.cpython-312.pyc,,
cudf_polars/dsl/expressions/__pycache__/string.cpython-312.pyc,,
cudf_polars/dsl/expressions/__pycache__/struct.cpython-312.pyc,,
cudf_polars/dsl/expressions/__pycache__/ternary.cpython-312.pyc,,
cudf_polars/dsl/expressions/__pycache__/unary.cpython-312.pyc,,
cudf_polars/dsl/expressions/aggregation.py,sha256=XxC1d4cbQIvL6o6REQ2FlyxcdUbVyMl-gN4RDFHnLSQ,7950
cudf_polars/dsl/expressions/base.py,sha256=vfMoDxI7pr1mSft_ncwF3WwbJ8onjH4OKONaYWao9DA,7816
cudf_polars/dsl/expressions/binaryop.py,sha256=7Psj8BKnotNGopQw5W9hM6cP8fbng-OllxrkgYrvLzs,5070
cudf_polars/dsl/expressions/boolean.py,sha256=nj1vsvcvoDbx95xu56ElLyom-_9okOislKKlhAAQeMg,11768
cudf_polars/dsl/expressions/datetime.py,sha256=PerCsW_j3QlswAkhI3_daDfm8oQyzbdpHreSF9O1NSY,10073
cudf_polars/dsl/expressions/literal.py,sha256=CpfWj5XFqBNKqu3jaWgP13_HfSg6maXiKDjmY6pdoUE,3192
cudf_polars/dsl/expressions/rolling.py,sha256=TlGsveufu4EisB4JTifxqWTpP8Vt617THzVDmB-0gBQ,5676
cudf_polars/dsl/expressions/selection.py,sha256=RfGj0RbKairCNibfQCUtwbFiS4xv9fRoznycEKxE3ww,2520
cudf_polars/dsl/expressions/slicing.py,sha256=xaI-XzZvPzyLDwG0yZcIPII56OMEJDxem2piV4LBGI0,1185
cudf_polars/dsl/expressions/sorting.py,sha256=6XO0JktGGUJujADXrZoSBeJGDk80vSOCzboB7jOlL5Q,2789
cudf_polars/dsl/expressions/string.py,sha256=Bk28MhvAiBH19MRQlZ6puFi4Ydh6SCazO7fqSj_AkRY,29867
cudf_polars/dsl/expressions/struct.py,sha256=RrSqx25egte-TcRFqgGXpQOLWpGXlBsuvOy_a7K1W1o,4677
cudf_polars/dsl/expressions/ternary.py,sha256=J_85frSq5Hh2ERSXOIZlwiwFTEp9WASh2hPiCkbkbqM,1415
cudf_polars/dsl/expressions/unary.py,sha256=AAXJpiu8kFAjR8B37P1xtgjuISlsOc8X2SA2lKJlk4c,15387
cudf_polars/dsl/ir.py,sha256=mafgkruaJH64o8mp3E0MsjUKrWh11XBSaa3V_zHHP3A,86460
cudf_polars/dsl/nodebase.py,sha256=QbZHK9aUbdiE-Mp_NkkiuNvCnD8E3xzd9-GYKR8UqcM,4777
cudf_polars/dsl/to_ast.py,sha256=We0idh-0ckSz9nIZrGkeMg75XbnijRW2_DHVXZ9-a34,13595
cudf_polars/dsl/tracing.py,sha256=xPTyXNQ64PSuV4_t5z6_GGJ1V_m4sFxxHiYEDp64Ofk,383
cudf_polars/dsl/translate.py,sha256=FHsUzcuCqO-mW2VJeom3c6wT49kE1ss5fCUA6vxmYMM,29818
cudf_polars/dsl/traversal.py,sha256=dzOHVaRj0wTYQd5h-JnjQrj4DffEfq1gdENYcDk5Eis,5729
cudf_polars/dsl/utils/__init__.py,sha256=JL26nlMAbcdL8ZE4iXRrMOEVSTEZU1P5y9WvxTEDdnY,199
cudf_polars/dsl/utils/__pycache__/__init__.cpython-312.pyc,,
cudf_polars/dsl/utils/__pycache__/aggregations.cpython-312.pyc,,
cudf_polars/dsl/utils/__pycache__/groupby.cpython-312.pyc,,
cudf_polars/dsl/utils/__pycache__/naming.cpython-312.pyc,,
cudf_polars/dsl/utils/__pycache__/replace.cpython-312.pyc,,
cudf_polars/dsl/utils/__pycache__/reshape.cpython-312.pyc,,
cudf_polars/dsl/utils/__pycache__/rolling.cpython-312.pyc,,
cudf_polars/dsl/utils/__pycache__/windows.cpython-312.pyc,,
cudf_polars/dsl/utils/aggregations.py,sha256=0ajzUHQoKf2AlU9-z-4aPfZh2R7EbhapIF8mdZVXub4,10520
cudf_polars/dsl/utils/groupby.py,sha256=vyAZp8yHxM5yiij2Q7fpc_rVbXCNJFGO-wmtc87fBAQ,2500
cudf_polars/dsl/utils/naming.py,sha256=ydp_BYYAt3mG7JHfi9Snp3dDNzdQZD6F2sAMEmT4OYA,737
cudf_polars/dsl/utils/replace.py,sha256=8ns_TpbG1Hh8ZJejRyGA6KCu5t-TvUaM009AO8J98vc,1612
cudf_polars/dsl/utils/reshape.py,sha256=Q13_0tIjgtMocGRFciPa1GcMxc2ClqqZf1mujl7w1kw,2397
cudf_polars/dsl/utils/rolling.py,sha256=f63BzOOsyFiFCk3jD_Uh1HaY7GsmIfM598PWb70fN74,3469
cudf_polars/dsl/utils/windows.py,sha256=QLbAacNj1AahCoQcWN_3k5raKUUTwHE4dYpGBRiavSs,5157
cudf_polars/experimental/__init__.py,sha256=S2oI2K__woyPQAAlFMOo6RTMtdfIZxmzzAO92VtJgP4,256
cudf_polars/experimental/__pycache__/__init__.cpython-312.pyc,,
cudf_polars/experimental/__pycache__/base.cpython-312.pyc,,
cudf_polars/experimental/__pycache__/dask_registers.cpython-312.pyc,,
cudf_polars/experimental/__pycache__/dispatch.cpython-312.pyc,,
cudf_polars/experimental/__pycache__/distinct.cpython-312.pyc,,
cudf_polars/experimental/__pycache__/explain.cpython-312.pyc,,
cudf_polars/experimental/__pycache__/expressions.cpython-312.pyc,,
cudf_polars/experimental/__pycache__/groupby.cpython-312.pyc,,
cudf_polars/experimental/__pycache__/io.cpython-312.pyc,,
cudf_polars/experimental/__pycache__/join.cpython-312.pyc,,
cudf_polars/experimental/__pycache__/parallel.cpython-312.pyc,,
cudf_polars/experimental/__pycache__/repartition.cpython-312.pyc,,
cudf_polars/experimental/__pycache__/scheduler.cpython-312.pyc,,
cudf_polars/experimental/__pycache__/select.cpython-312.pyc,,
cudf_polars/experimental/__pycache__/shuffle.cpython-312.pyc,,
cudf_polars/experimental/__pycache__/sort.cpython-312.pyc,,
cudf_polars/experimental/__pycache__/spilling.cpython-312.pyc,,
cudf_polars/experimental/__pycache__/utils.cpython-312.pyc,,
cudf_polars/experimental/base.py,sha256=N3_ArpNyxU4N-Tl6kheSRsW4VL4Ceqfj_KxOw4QwYVs,3968
cudf_polars/experimental/benchmarks/__init__.py,sha256=XiT8hL6V9Ns_SSXDXkzoSWlXIo6aFLDXUHLLWch23Ok,149
cudf_polars/experimental/benchmarks/__pycache__/__init__.cpython-312.pyc,,
cudf_polars/experimental/benchmarks/__pycache__/pdsds.cpython-312.pyc,,
cudf_polars/experimental/benchmarks/__pycache__/pdsh.cpython-312.pyc,,
cudf_polars/experimental/benchmarks/__pycache__/utils.cpython-312.pyc,,
cudf_polars/experimental/benchmarks/pdsds.py,sha256=8QJ0_Dw1Jdl9rcaAXvYaWxWNIy8phqozioMPEpSsIro,6666
cudf_polars/experimental/benchmarks/pdsds_queries/__init__.py,sha256=pkjkRg2qJCMbhBpD9cIxcjsgMOZXXliWJPZIgZpcUQA,151
cudf_polars/experimental/benchmarks/pdsds_queries/__pycache__/__init__.cpython-312.pyc,,
cudf_polars/experimental/benchmarks/pdsds_queries/__pycache__/q1.cpython-312.pyc,,
cudf_polars/experimental/benchmarks/pdsds_queries/q1.py,sha256=NTvgxMJUB9xH2llo6_SWO7JQNwxEoK9nQ-mnRCsYf9Y,3100
cudf_polars/experimental/benchmarks/pdsh.py,sha256=Pag9gMh_tZbtzNXTQT0KTq8y93elPzSwjujCM8sP2K4,31127
cudf_polars/experimental/benchmarks/utils.py,sha256=9iNckfrcbqfyRhjODycQ87_TJw94GL-ieTYw0_2a8m4,23672
cudf_polars/experimental/dask_registers.py,sha256=bGU6nEh-rQd6lMPaEhJUdkVrkCFSjknb8IwB0EeMnrs,7780
cudf_polars/experimental/dispatch.py,sha256=D3J-mwNWbcbATsjiaCwCea8JUc0G6Ue-ckI4tN8YOLs,2398
cudf_polars/experimental/distinct.py,sha256=nyiAeg_jJ-_eiw6mzPTluQ3OZe1p1NiGlOQU8AP6q6A,6800
cudf_polars/experimental/explain.py,sha256=o2HNzZr1X0P-Q68B7iPgE52WMf_G1XfiZX9xQ1aFeUM,3646
cudf_polars/experimental/expressions.py,sha256=nYR4W9oNW_oTwNN0zP3Owd1uo_JsVEm8atPgdJYdH1I,16930
cudf_polars/experimental/groupby.py,sha256=uflm7QJC_TihxRn5q11yegWwOZCQC_CUPFxIijq7WTs,11044
cudf_polars/experimental/io.py,sha256=APaghBy9Ps44JQxUijFu3Ey0EGdqi-pOhFJPj_rMpNE,30345
cudf_polars/experimental/join.py,sha256=ZqeFzPkodvqWUokjzzN2AAcE-4v_ytpPbLG09CNqp2o,12724
cudf_polars/experimental/parallel.py,sha256=kY0WDdDNY27SBlnxQwdiymJh0SNpGePW6bS9tUQoYoY,11590
cudf_polars/experimental/repartition.py,sha256=o1qtstaB_dBaYjkmsmfnveZ6T66A9XGwzFEBUucfyrk,2124
cudf_polars/experimental/scheduler.py,sha256=ieL7bdxTqlmd8MO37JCaCoqhyDRZNTLnPFUme3hv6SQ,4203
cudf_polars/experimental/select.py,sha256=uSV0NfMQ1DMhzExZQDOWRfQ-6Rf5CBmcQ_MRzBvKhUU,5240
cudf_polars/experimental/shuffle.py,sha256=t3ZYWRacLQPphGB2vHuCDJRWkYCvEDrmDKsTniQFQog,9244
cudf_polars/experimental/sort.py,sha256=OAY_Us0IXfYPy6NRwqH3JsSE00lg63o83uuve6KLFiY,1633
cudf_polars/experimental/spilling.py,sha256=OVpH9PHYNJcYL-PAB0CvoAil_nJW0VepLvcIrrAUdlc,4255
cudf_polars/experimental/utils.py,sha256=rFLftMaLflor4GCagtDLk5FSzmC41YBdtX0Xt8usWg0,3630
cudf_polars/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
cudf_polars/testing/__init__.py,sha256=0MnlTjkTEqkSpL5GdMhQf4uXOaQwNrzgEJCZKa5FnL4,219
cudf_polars/testing/__pycache__/__init__.cpython-312.pyc,,
cudf_polars/testing/__pycache__/asserts.cpython-312.pyc,,
cudf_polars/testing/__pycache__/io.cpython-312.pyc,,
cudf_polars/testing/__pycache__/plugin.cpython-312.pyc,,
cudf_polars/testing/asserts.py,sha256=JODwAH8Rg5i0uX3pntloXVkJ5T1sWJjmxdz8boqku2Q,14806
cudf_polars/testing/io.py,sha256=sw9aT4wXLRMKxuH2rCwy7wV3c8pqEdqZdXn2jQSb-jM,2221
cudf_polars/testing/plugin.py,sha256=0NLBBgGh_bkq6ZVCuRu9Paafurg2bwQcgCoUz1vkKKA,24682
cudf_polars/typing/__init__.py,sha256=Q1iVabv-7etFo01UxT6cz-zgZa_9_WYA8D8QHnjZuTg,5022
cudf_polars/typing/__pycache__/__init__.cpython-312.pyc,,
cudf_polars/utils/__init__.py,sha256=urdV5MUIneU8Dn6pt1db5GkDG0oY4NsFD0Uhl3j98l8,195
cudf_polars/utils/__pycache__/__init__.cpython-312.pyc,,
cudf_polars/utils/__pycache__/config.cpython-312.pyc,,
cudf_polars/utils/__pycache__/conversion.cpython-312.pyc,,
cudf_polars/utils/__pycache__/dtypes.cpython-312.pyc,,
cudf_polars/utils/__pycache__/sorting.cpython-312.pyc,,
cudf_polars/utils/__pycache__/timer.cpython-312.pyc,,
cudf_polars/utils/__pycache__/versions.cpython-312.pyc,,
cudf_polars/utils/config.py,sha256=Jnum-nQe-ClCrqRo0dkMBNT7qJtymi6EWoCJ4bUnlNU,22671
cudf_polars/utils/conversion.py,sha256=k_apLbSR-MiYYlQBGrzYOInuvcbfSi-il-o9nkovdXQ,1042
cudf_polars/utils/dtypes.py,sha256=BSAk56cqty3AzCfrcXP2DxZ2Hx_4nqdd_wahtY9QVpM,2927
cudf_polars/utils/sorting.py,sha256=Mqb_KLsYnKU8p1dDan2mtlIQl65RqwM78OlUi-_Jj0k,1725
cudf_polars/utils/timer.py,sha256=KqcXqOcbovsj6KDCwaxl70baQXjuod43rABrpQkE78M,1005
cudf_polars/utils/versions.py,sha256=pkr5YVy6D4qpic136nEx7I5HR2SYSiyCMMNpDDpL9Nw,789
cudf_polars_cu12-25.8.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
cudf_polars_cu12-25.8.0.dist-info/METADATA,sha256=C9zPQw6k6NRAC2yhUJqxEhuXmcxnRmXmiN3Gnjn9WfA,4597
cudf_polars_cu12-25.8.0.dist-info/RECORD,,
cudf_polars_cu12-25.8.0.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
cudf_polars_cu12-25.8.0.dist-info/licenses/LICENSE,sha256=4YCpjWCbYMkMQFW47JXsorZLOaP957HwmP6oHW2_ngM,11348
cudf_polars_cu12-25.8.0.dist-info/top_level.txt,sha256=w2bOa7MpuyapYgZh480Znh4UzX7rSWlFcYR1Yo6QIPs,12
