Metadata-Version: 2.1
Name: QuantLib-Python
Version: 1.18
Summary: Backward-compatible meta-package for the QuantLib module
Home-page: http://quantlib.org
Author: QuantLib Team
Author-email: <EMAIL>
License: BSD 3-Clause
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: Intended Audience :: End Users/Desktop
Classifier: License :: OSI Approved :: BSD License
Classifier: Natural Language :: English
Classifier: Programming Language :: C++
Classifier: Programming Language :: Python
Classifier: Topic :: Scientific/Engineering
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX
Classifier: Operating System :: Unix
Classifier: Operating System :: MacOS
Requires-Dist: QuantLib


This module is provided for backward compatibility.
Use "pip install QuantLib" instead.


